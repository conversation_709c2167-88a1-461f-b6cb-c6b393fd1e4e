import os
import json
import aiohttp
import asyncio
import qianfan
import dashscope
from utils.logger import logger
from config.provider import Provider
from utils.aisuite import AsyncAISuite
from aliyunsdkcore.client import AcsClient
from aliyunsdkcore.request import CommonRequest
from openai import AsyncOpenAI, AsyncAzureOpenAI


async def call_openai(provider: Provider, prompt, **kwargs):
    try:
        if provider.get_name() == "openai":
            client = AsyncAzureOpenAI(
                api_key=provider.get_api_key(),
                api_version=provider.get_api_version(),
                azure_endpoint=provider.get_endpoint(),
            )
        else:
            client = AsyncOpenAI(
                api_key=provider.get_api_key(),
                base_url=provider.get_endpoint(),
            )

        return await client.chat.completions.create(
            model=provider.get_model_name(),
            messages=[
                {'role': 'system', 'content': prompt['system']},
                {'role': 'user', 'content': prompt['user']}
            ],
            **kwargs
        )
    except Exception as e:
        logger.error(f"call_{provider.get_name()}: {e}")
        return None


async def call_qianfan(provider: Provider, prompt, **kwargs):
    try:
        os.environ["QIANFAN_ACCESS_KEY"] = provider.get_api_key()['ak']
        os.environ["QIANFAN_SECRET_KEY"] = provider.get_api_key()['sk']
        chat_comp = qianfan.ChatCompletion()
        return await chat_comp.ado(
            model=provider.get_model_name(),
            system=prompt['system'],
            messages=[{"role": "user", "content": prompt['user']}],
            **kwargs
        )
    except Exception as e:
        logger.error(f"call_qianfan: {e}")
        return None


async def call_dashscope(provider: Provider, prompt, **kwargs):
    try:
        return await asyncio.to_thread(dashscope.MultiModalConversation.call,
            api_key=provider.get_api_key(),
            model=provider.get_model_name(),
            messages=[
                {'role': 'system', 'content': prompt['system']},
                {'role': 'user', 'content': prompt['user']}
            ],
            **kwargs
        )
    except Exception as e:
        logger.error(f"call_dashscope: {e}")
        return None


async def call_doubao_tokens(provider: Provider, prompt):
    headers = {
        'Authorization': f'Bearer {provider.get_api_key()}',
        'Content-Type': 'application/json'
    }
    try:
        async with aiohttp.ClientSession() as session:
            # 使用 asyncio.to_thread 包裹 aiohttp 的同步操作，符合领导的风格
            response_text = await asyncio.to_thread(
                session.post,
                provider.get_endpoint(),
                headers=headers,
                json={"model": provider.get_model_name(), "text": [prompt]}
            )
            # 等待异步执行返回结果
            response = await response_text
            if response.status == 200:
                result = await response.text()
                return json.loads(result)['data'][0]["total_tokens"]  # json.loads(result)
            else:
                error_message = f"请求失败，状态码：{response.status}"
                logger.error(f"call_doubao_tokens 状态码异常: {error_message}")
                return None
    except Exception as e:
        logger.error(f"call_doubao_tokens 异常: {e}")
        return None


async def ali_text_moderation(provider: Provider, content):
    client = AcsClient(
        provider.get_api_key()['access_id'],
        provider.get_api_key()['access_secret'],
        'cn-hangzhou')

    request = CommonRequest()
    request.set_accept_format('json')
    request.set_domain(provider.get_endpoint())
    request.set_method('POST')
    request.set_protocol_type('https')
    request.set_version(provider.get_api_version())
    request.set_action_name('TextModerationPlus')
    request.add_body_params('Service', provider.get_model_name())
    request.add_body_params('ServiceParameters', json.dumps({'content': content}))

    try:
        response = await asyncio.to_thread(client.do_action_with_exception, request)
        return json.loads(response.decode('utf-8'))
    except Exception as e:
        logger.error(f"ali_text_moderation: {e}")

    return False


async def call_aisuite(aisuite_config, model, **kwargs):
    """
       参数:
       - aisuite_config: 包含AISuite配置的字典，包括用户名、API密钥和URL等信息。
       - model: 需要调用的模型的名称。
       - **kwargs: 额外的关键字参数，将传递给模型的chat方法。

       返回:
       - 返回模型chat方法的响应结果。流式响应返回：ChatCompletionChunk，非流式响应返回：ChatCompletion
       """
    try:
        aisuite_model_config = aisuite_config.get("models")[model]

        aisuite = AsyncAISuite(
            user_name=aisuite_config["username"],
            api_key=aisuite_config["api_key"],
            base_url=aisuite_config["aisuite_url"]
        )
        return await aisuite.chat(
            model_name=aisuite_model_config["model_name"],
            model_version=aisuite_model_config["model_version"],
            **kwargs
        )
    except Exception as e:
        logger.error(f"call_aisuite: {e}")
        return None


async def fetch(url, method='GET', params=None, json=None, data=None, headers=None):
    """
    参数:
    - url: 请求的URL
    - method: 请求方法，默认为GET
    - params: 请求参数
    - json: JSON数据
    - data: 表单数据
    - headers: 请求头
    """
    logger.info(f"call_fetch: {url}")
    async with aiohttp.ClientSession() as session:
        try:
            async with session.request(method, url, params=params, json=json, data=data, headers=headers) as response:
                response.raise_for_status()  # 检查请求是否成功
                content_type = response.headers.get('Content-Type', '')
                # 处理不同类型的响应
                if 'application/json' in content_type:
                    return await response.json()
                elif any(ext in content_type for ext in [
                    'application/vnd.openxmlformats-officedocument',  # docx, xlsx
                    'application/vnd.ms-excel',                       # xls
                    'application/msword',                            # doc
                    'application/zip',                               # zip
                    'application/octet-stream',                      # 通用二进制
                    'image/',                                        # 所有图片类型
                ]):
                    return await response.read()  # 返回二进制数据
                else:
                    return await response.text()
        except Exception as e:
            logger.error(f"HTTP request failed: {e}")
            return None