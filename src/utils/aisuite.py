import asyncio
import json
import aiohttp
from openai.types.chat import Cha<PERSON><PERSON><PERSON>pletionChunk, ChatCompletion

from utils.logger import logger


class AsyncAISuite:
    def __init__(self, user_name: str, api_key: str, base_url: str):
        self.user_name = user_name
        self.user_api_key = api_key
        self.base_url = base_url

    # 封装请求方法
    async def chat(self, model_name, model_version, environment="dev", **kwargs):
        try:
            # 生成请求头
            headers = {}
            # 生成请求体
            data = {
                "environment": environment,
                "user_name": self.user_name,
                "user_api_key": self.user_api_key,
                "model_name": model_name,
                "model_version": model_version,
                "input_params": {
                    **kwargs
                }
            }
            # 进行请求，返回响应数据
            if kwargs.get("stream", False):
                return self.res_chat_completion_chunk(self.base_url, headers, data)
            else:
                return await self.res_chat_completion(self.base_url, headers, data)
        except aiohttp.ClientError as e:
            logger.error(f"请求异常：{e}", exc_info=True)
        except ValueError as e:
            logger.error(f"{e}", exc_info=True)
        except Exception as e:
            logger.error(f"调用模型出现异常：{e}", exc_info=True)

    # 封装流式请求方法
    async def res_chat_completion_chunk(self, url, headers, data):
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=data) as response:
                if response.status == 200:
                    try:
                        buffer = ""
                        async for chunk in response.content.iter_chunked(10240):
                            chunk_str = chunk.decode('utf-8')
                            buffer += chunk_str
                            while True:
                                try:
                                    # 去除前后的 "data:" 和 "\n\n"
                                    buffer = await self.clean_data(buffer)
                                    json_obj, index = json.JSONDecoder().raw_decode(buffer)
                                    buffer = buffer[index:].lstrip()
                                    # 将 finish_reason normal 转为 stop， normal为百度特有的，向openai兼容
                                    await self.format_finishing_reason(json_obj)
                                    # 去除空行，过滤掉object为非ChatCompletionChunk的类型
                                    object = json_obj.get("object")
                                    if object:
                                        result = ChatCompletionChunk(**json_obj)
                                        yield result
                                except json.JSONDecodeError:
                                    break
                        # 模型返回结果异常处理
                        if buffer:
                            raise ValueError(buffer)
                    except asyncio.TimeoutError:
                        raise ValueError("请求超时，请重试。")
                    except aiohttp.ClientPayloadError as e:
                        raise ValueError(f"数据传输异常：{e}")
                    except Exception as e:
                        raise ValueError(f"请求异常：{e}")
                else:
                    raise ValueError(f"请求失败，状态码：{response.status}")

    # 封装非流式请求方法
    async def res_chat_completion(self, url, headers, data):
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=data) as response:
                if response.status == 200:
                    try:
                        content = await response.read()
                        # 去除前后的 "data:" 和 "\n\n"
                        buffer = await self.clean_data(content.decode("utf-8"))
                        json_obj = json.loads(buffer)
                        # 将 finish_reason normal 转为 stop， normal为百度特有的，向openai兼容
                        await self.format_finishing_reason(json_obj)
                        return ChatCompletion(**json_obj)
                    except json.JSONDecodeError:
                        raise ValueError(content.decode("utf-8"))
                    except asyncio.TimeoutError:
                        raise ValueError("请求超时，请重试。")
                    except aiohttp.ClientPayloadError as e:
                        raise ValueError(f"数据传输异常：{e}")
                    except Exception as e:
                        raise ValueError(f"请求异常：{e}")
                else:
                    raise ValueError(f"请求失败，状态码：{response.status}")

    async def format_finishing_reason(self, json_obj):
        if json_obj.get("choices") and json_obj["choices"][0].get("finish_reason") == "normal":
            json_obj["choices"][0]["finish_reason"] = "stop"

    # 去除前后的 "data:" 和 "\n\n"
    async def clean_data(self, buffer):
        buffer = buffer.strip()
        if buffer.startswith("data:"):
            buffer = buffer[5:]
        if buffer.endswith("\n\n"):
            buffer = buffer[:-2]
        return buffer.strip()