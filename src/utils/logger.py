import os
import logging
from concurrent_log_handler import ConcurrentTimedRotatingFileHandler


class LogManager:
    """
    A class for managing logs with customizable levels and handlers.
    """

    def __init__(self, name=__name__, log_level=logging.INFO, log_dir="logs"):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(log_level)

        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        log_file = os.path.join(log_dir, "app.log")
        handler = ConcurrentTimedRotatingFileHandler(log_file, when="midnight", interval=1, backupCount=7)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)

    def debug(self, message):
        self.logger.debug(message)

    def info(self, message):
        self.logger.info(message)

    def warning(self, message):
        self.logger.warning(message)

    def error(self, message, exc_info=False):
        if exc_info:
            self.logger.error(message, exc_info=exc_info)
        else:
            self.logger.error(message)

    def critical(self, message, exc_info=False):
        if exc_info:
            self.logger.critical(message, exc_info=exc_info)
        else:
            self.logger.critical(message)


logger = LogManager()