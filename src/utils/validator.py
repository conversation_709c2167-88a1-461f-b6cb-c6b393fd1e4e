def check_answer(json_dict):
    # 检查 materialList 字段
    if 'materialList' in json_dict and isinstance(json_dict['materialList'], list):
        for item in json_dict['materialList']:
            if not isinstance(item, dict) or 'materialId' not in item or 'materialContent' not in item:
                raise ValueError("materialList 中的元素格式有误")
    else:
        raise ValueError("materialList 字段不存在或格式不正确")

    # 检查 answerKeys 字段
    if 'answerKeys' in json_dict and isinstance(json_dict['answerKeys'], list):
        for item in json_dict['answerKeys']:
            if not isinstance(item, dict) or 'answerKeyId' not in item or 'answerKey' not in item:
                raise ValueError("answerKeys 中的元素格式有误")
    else:
        raise ValueError("answerKeys 字段不存在或格式不正确")

    # 检查 answerRequirement 字段
    if not ('answerRequirement' in json_dict and isinstance(json_dict['answerRequirement'], str)):
        raise ValueError("answerRequirement 字段格式不正确")
    # 检查 answerRequirement 字段
    if not ('questionType' in json_dict and isinstance(json_dict['questionType'], int)):
        raise ValueError("questionType 字段格式不正确")# 检查 answerRequirement 字段
    if not ('answerKeyIds' in json_dict and isinstance(json_dict['answerKeyIds'], str)):
        raise ValueError("answerKeyIds 字段格式不正确")
    

def check_correction_structure(data):
    # 定义必要的顶级字段
    required_top_level_fields = [
        "answerId", "questionId", "userAnswer", "questionType",
        "version", "correctId",  "questionKeyPoints",
        "answer", "answerRequirement", "questionDeductionPoints", "score"
    ]

    # 检查顶级字段是否存在
    for field in required_top_level_fields:
        if field not in data:
            return False, f"缺少顶级字段: {field}"

    if not isinstance(data['questionType'], int):
        return False, "questionType 字段格式不正确"
    
    if not isinstance(data["score"], (int, float)):
        return False, "score 字段格式不正确"
    
    # 检查 questionKeyPoints 列表
    if not isinstance(data["questionKeyPoints"], list):
        return False, "questionKeyPoints 不是列表类型"
    for point in data["questionKeyPoints"]:
        required_point_fields = ["keyPointContent", "keyPointId", "score", "answerLocation"]
        for field in required_point_fields:
            if field not in point:
                return False, f"questionKeyPoints 中的元素缺少字段: {field}"
        if not isinstance(point["keyPointId"], int):
            return False, "questionKeyPoints 中的 keyPointId 不是整数类型"
        if not isinstance(point["score"], (int, float)):
            return False, "questionKeyPoints 中的 score 不是数字类型"

    # 检查 questionDeductionPoints 列表
    if not isinstance(data["questionDeductionPoints"], list) or len(data["questionDeductionPoints"]) == 0:
        return False, "questionDeductionPoints 不是列表类型 或 长度为0"
    for deduction in data["questionDeductionPoints"]:
        required_deduction_fields = ["deductionPointId", "deductionPointContent", "value"]
        for field in required_deduction_fields:
            if field not in deduction:
                return False, f"questionDeductionPoints 中的元素缺少字段: {field}"
        if not isinstance(deduction["deductionPointId"], int):
            return False, "questionDeductionPoints 中的 deductionPointId 不是整数类型"
        if not isinstance(deduction["value"], list) or len(deduction["value"]) != 2:
            return False, "questionDeductionPoints 中的 value 不是包含两个元素的列表"
        for val in deduction["value"]:
            if not isinstance(val, (int, float)):
                return False, "questionDeductionPoints 中的 value 元素不是数字类型"
        if deduction["value"][1] < 0:
            return False, "questionDeductionPoints 中 value 的第二个元素不能小于0"

    # 文章写作字段检查
    if data["questionType"] == 5:
        # 检查必要字段
        article_fields = [
            "questionBonusPoints", "images", "centralArgument",
            "gearShiftSettings", "answerLength", "exceedingWord",
            "lowerWordLimit", "upperWordLimit", "materialList"
        ]
        for field in article_fields:
            if field not in data:
                return False, f"文章写作缺少必要字段: {field}"
            
        # 检查 questionBonusPoints 列表
        if not isinstance(data["questionBonusPoints"], list):
            return False, "questionBonusPoints 不是列表类型"
        for bonus in data["questionBonusPoints"]:
            required_bonus_fields = ["bonusPointId", "bonusPointContent", "value"]
            for field in required_bonus_fields:
                if field not in bonus:
                    return False, f"questionBonusPoints 中的元素缺少字段: {field}"
            if not isinstance(bonus["bonusPointId"], int):
                return False, "questionBonusPoints 中的 bonusPointId 不是整数类型"
            if not isinstance(bonus["value"], list) or len(bonus["value"]) != 2:
                return False, "questionBonusPoints 中的 value 不是包含两个元素的列表"
            for val in bonus["value"]:
                if not isinstance(val, (int, float)):
                    return False, "questionBonusPoints 中的 value 元素不是数字类型"
            if bonus["value"][1] < 0:
                return False, "questionBonusPoints 中 value 的第二个元素不能小于0"

        # 检查 images（None 或列表）
        if data["images"] is not None:
            if not isinstance(data["images"], list):
                return False, "images 类型错误"
            for image in data["images"]:
                required_image_fields = ["id", "url"]
                for field in required_image_fields:
                    if field not in image:
                        return False, f"images 中的元素缺少字段: {field}"
                if not isinstance(image["id"], int):
                    return False, "images 中的元素 id 不是整数类型"
                if not isinstance(image["url"], str) or not image["url"].startswith("http"):
                    return False, "images 中的元素 url 不是http开头的字符串"
            
        # 检查 centralArgument
        if not isinstance(data["centralArgument"], str):
            return False, "centralArgument 不是字符串类型"
        
        # 检查 gearShiftSettings
        if not isinstance(data["gearShiftSettings"], list) or len(data["gearShiftSettings"]) != 4:
            return False, "gearShiftSettings 不是列表类型 或 长度不等于4"
        gear_shift_type = [1, 2, 3, 4]
        gear_shift_type_list = []
        for gear_shift in data["gearShiftSettings"]:
            required_gear_shift_fields = ["type", "wordsLower", "wordsUpper", "scoreLower", "scoreUpper"]
            for field in required_gear_shift_fields:
                if field not in gear_shift:
                    return False, f"gearShiftSettings 缺少字段: {field}"
            if not isinstance(gear_shift["type"], int) or gear_shift["type"] not in gear_shift_type:
                return False, "gearShiftSettings 中的 type 不是整数类型 或 不在1-4之间"
            gear_shift_type_list.append(gear_shift["type"])
            number_keys = ["wordsLower", "wordsUpper", "scoreLower", "scoreUpper"]
            for key in number_keys:
                if not isinstance(gear_shift[key], (int, float)):
                    return False, f"gearShiftSettings 中的 {key} 不是数字类型"
                if gear_shift[key] < 0:
                    return False, f"gearShiftSettings 中的 {key} 不能小于0"
        if set(gear_shift_type_list) != set(gear_shift_type):
            return False, "gearShiftSettings 中的 type 列表不完整"
            
        # 检查 exceedingWord
        if data["exceedingWord"] is not None:
            if not isinstance(data["exceedingWord"], dict):
                return False, "exceedingWord 类型错误"
        
        # 检查 materialList
        if not isinstance(data["materialList"], list) or len(data["materialList"]) == 0:
            return False, "materialList 不是列表类型 或 长度为0"
        for material in data["materialList"]:
            required_material_fields = ["id", "content"]
            for field in required_material_fields:
                if field not in material:
                    return False, f"materialList 中的元素缺少字段: {field}"
            if not isinstance(material["id"], int):
                return False, "materialList 中的元素 id 不是整数类型"
            if not isinstance(material["content"], str):
                return False, "materialList 中的元素 content 不是字符串类型"
        
        # 检查数字类型
        int_keys = ["answerLength", "lowerWordLimit", "upperWordLimit"]
        for key in int_keys:
            if not isinstance(data[key], int):
                return False, f"{key} 不是整数类型"
            if data[key] < 0:
                return False, f"{key} 不能小于0"
        
        if data["lowerWordLimit"] > data["upperWordLimit"]:
            return False, "lowerWordLimit 大于 upperWordLimit"
        
    return True, "JSON 结构检查通过"