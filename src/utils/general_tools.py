# encoding: utf-8
# @author: hanyuxi
# @file: general_tools.py
# @time: 2025/2/14 11:04
import re
from bs4 import BeautifulSoup
from utils.logger import logger
from services import ai_providers
from services.ai_service import AIService


def remove_tags_bs4(text):
    """使用BeautifulSoup提取纯文本"""
    soup = BeautifulSoup(text, 'html.parser')
    return soup.get_text()

def clean_invalid_json_escape(s):
    # 替换非法的 \xNN 转义为等价的 Unicode 转义 \u00NN
    s = re.sub(r'\\x([0-9a-fA-F]{2})', lambda m: '\\u00' + m.group(1), s)
    return s

def extract_json_from_markdown(text: str):
    """
    从 Markdown 文本中提取 JSON 内容。

    :param text: 包含 JSON 的 Markdown 文本
    :return: 提取的 JSON 字符串或 None
    """
    text = clean_invalid_json_escape(text)
    match = re.search(r"```json(.*?)```", text, re.DOTALL)
    return match.group(1).strip() if match else text


async def get_use_tokens(response):
    # 处理 ChatCompletionChunk 格式
    if response and hasattr(response, 'usage') and response.usage:
        usage = response.usage
        # openai 格式
        if 'prompt_tokens' in dir(usage):
            prompt_tokens = usage.prompt_tokens
            completion_tokens = usage.completion_tokens
        # 千问 dashscope 格式
        else:
            prompt_tokens = usage.input_tokens
            completion_tokens = usage.output_tokens
    # 处理火山智能体 bot 格式
    elif response and hasattr(response, 'bot_usage') and response.bot_usage:
        usage = response.bot_usage['model_usage'][0]
        prompt_tokens = usage['prompt_tokens']
        completion_tokens = usage['completion_tokens']
    # 处理百度千帆的 QfResponse 格式
    elif response and 'usage' in response and response["is_end"]:
        usage = response["usage"]
        prompt_tokens = usage["prompt_tokens"]
        completion_tokens = usage["completion_tokens"]
    else:
        prompt_tokens, completion_tokens = 0, 0
    return prompt_tokens, completion_tokens


async def get_tokens(prompt):
    """
    根据 prompt 的 token 数量选择合适的模型名称。
    """
    try:
        provider = ai_providers.get_provider_by_name('count_tokens')[0]
        # 构造输入字符串并统计 token 数量
        input_string = f"{prompt.get('system', '')}{prompt.get('user', '')}"
        tokens = await AIService.count_tokens(provider, input_string)
        return tokens

    except Exception as ee:
        logger.error(f"get_tokens error: {ee}")
        return None


async def risk_assessment(content):
    """
    使用阿里模型对内容进行风险评估
    """
    provider = ai_providers.get_provider_by_name('ali')[0]
    
    risk_model = await AIService.ali_text_moderation(provider, content)
    if risk_model["Code"] == 200 and risk_model["Data"]["RiskLevel"] != "none":
        return risk_model["Data"]["Result"][0]["RiskWords"]
    return None


def convert_chinese_punctuation_to_english(text):
    # 创建中文字符到英文字符的映射表
    chinese_punctuations = '，。！？；：“”‘’（）【】《》'
    english_punctuations = ',.!?;:\'\'\'\'()[]<>'
    translation_table = str.maketrans(chinese_punctuations, english_punctuations)
    return text.translate(translation_table)


def slice_and_match(slices, data):
    matched_content = []
    not_matched_content = []
    for s in slices:
        # 如果某个切片并未找到，那么将他设置为空字符串
        if s in data:
            matched_content.append(s)
        else:
            not_matched_content.append(s)
    return matched_content, not_matched_content