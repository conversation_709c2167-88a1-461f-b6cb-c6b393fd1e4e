import time
from functools import wraps
from config.settings import config
from utils.logger import logger

log_config = config['Log']

# 同步执行时间装饰器
def timing_decorator(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        if log_config.get('level') == "DEBUG":
            start_time = time.time()
            result = func(*args, **kwargs)
            elapsed_time = time.time() - start_time
            logger.info(f"{func.__name__} took {elapsed_time:.4f} seconds")
        else:
            result = func(*args, **kwargs)
        return result
    return wrapper


# 异步执行时间装饰器
def async_timing_decorator(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        if log_config.get('level') == "DEBUG":
            start_time = time.time()
            result = await func(*args, **kwargs)
            elapsed_time = time.time() - start_time
            logger.info(f"{func.__name__} took {elapsed_time:.4f} seconds")
        else:
            result = await func(*args, **kwargs)
        return result
    return wrapper
        