# encoding: utf-8
# @author: hanyuxi
# @file: score_tools.py
# @time: 2025/2/26 11:42
# 分数的一些通用工具
import re
from utils.logger import logger
from utils.general_tools import convert_chinese_punctuation_to_english


class score:

    @classmethod
    def create_answer_score_map(cls, question_key_points):
        """
        创建一个答案要点到其分数的映射
        :param question_key_points: 问题的要点信息
        :return: 答案要点到其分数的映射
        """
        return {item['keyPointId']: item["score"] for index, item in enumerate(question_key_points)}

    @classmethod
    def process_point_score(cls, item, answer_score):
        """
        处理回答要点的分数，包括类型转换、负数处理、分数上限调整和倍数调整
        :param item: 回答要点的信息
        :param answer_score: 答案要点的分数
        :return: 处理后的回答要点分数
        """
        point_score = get_clear_score(item["要点得分"])

        # 如果分数为负数，改成0分
        point_score = max(0, point_score)
        # 如果回答要点分数大于答案要点分数，修改成答案要点的满分
        point_score = min(point_score, answer_score)
        # 如果分数不是0.5的倍数，调整成小于答案要点的分数且是0.5的倍数
        point_score = cls.adjust_to_half_multiple(point_score, answer_score)
        # 将关键词遗漏的进行统计
        if item['回答要点分类'].strip() == "遗漏":
            # 发现模型会有将遗漏要点也给分的情况，所以当遇到遗漏时候，直接强制变成0
            point_score = 0
        return point_score

    @classmethod
    def adjust_to_half_multiple(cls, point_score, answer_score):
        """
        调整分数为0.5的倍数且不超过答案要点的分数
        :param point_score: 回答要点的分数
        :param answer_score: 答案要点的分数
        :return: 调整后的分数
        """
        if point_score % 0.5 != 0:
            point_score = (int(point_score * 2) + 1) / 2
            point_score = min(point_score, answer_score)
        return point_score

    @classmethod
    async def correct_score(cls, data, line_result):
        # 获取问题的要点信息
        question_key_points = data['keyPoint']['questionKeyPoints']
        # 创建一个答案要点到其分数的映射
        answer_score_map = cls.create_answer_score_map(question_key_points)
        # 处理每个回答要点的分数
        for index, item in enumerate(line_result):
            # 获取答案要点的分数
            answer_score = answer_score_map[item['keyPointId']]
            # 处理并获取回答要点的分数
            point_score = cls.process_point_score(item, answer_score)
            item["要点得分"] = point_score
        return line_result

    @classmethod
    def process_format_score(cls, format_score, answer_score):
        """
        处理格式得分
        """
        if not isinstance(format_score, (str, int, float)):
            return 0
        
        format_score = get_clear_score(format_score)
        # 如果分数不是0.5的倍数，调整成小于答案要点的分数且是0.5的倍数
        format_score = cls.adjust_to_half_multiple(format_score, answer_score)
        return format_score
    
    @classmethod
    def process_format_field(cls, content, score_text, max_score):
        """
        处理字段内容和分数
        
        Args:
            content: 字段内容
            score_text: 字段分数文本
            max_score: 最大分数
            
        Returns:
            tuple: (处理后的内容, 处理后的分数)
        """
        # 验证内容
        if not isinstance(content, str) or content.strip() in ["无", ""]:
            return "无", 0
        
        # 处理分数
        processed_score = cls.process_format_score(score_text, max_score)
        if processed_score <= 0:
            return "无", 0
            
        return content.strip(), processed_score
    
    @classmethod
    async def correct_format_score(cls, data, format_result):
        """
        处理格式得分，确保每个格式得分小于等于格式总分且为0.5的倍数
        """
        result = {}
        format_points = data["keyPoint"]["formatPoints"]
        
        # 提取落款信息
        signature_dict = format_result.get('回答落款', {}) if isinstance(format_result.get('回答落款'), dict) else {}
        signature_score_dict = format_result.get('回答落款得分', {}) if isinstance(format_result.get('回答落款得分'), dict) else {}
        
        # 处理各字段
        fields = [
            {'key': '回答标题', 'content': format_result.get('回答标题'), 'score_text': format_result.get('回答标题得分'), 'max_score': format_points['标题']},
            {'key': '回答称谓', 'content': format_result.get('回答称谓'), 'score_text': format_result.get('回答称谓得分'), 'max_score': format_points['称谓']},
            {'key': '发文主体', 'content': signature_dict.get('发文主体'), 'score_text': signature_score_dict.get('发文主体得分'), 'max_score': format_points['发文主体']},
            {'key': '发文日期', 'content': signature_dict.get('发文日期'), 'score_text': signature_score_dict.get('发文日期得分'), 'max_score': format_points['发文日期']}
        ]
        
        for field in fields:
            content, score = cls.process_format_field(field['content'], field['score_text'], field['max_score'])
            result[field['key']] = convert_chinese_punctuation_to_english(content)
            result[f"{field['key']}得分"] = score
        
        return result


def get_clear_score(score_text):
    try:
        score = round(float(str(score_text).strip().replace("分", "")), 2)
        return max(score, 0.0)  # 确保分数不小于0
    except Exception as e:
        logger.error(f"get_clear_score error: {e}")
        return 0


def get_format_score(score_text):
    return score_text if isinstance(score_text, (int, float)) and score_text > 0 else 0


def get_evaluation(total_score, obtained_score):
    ratio = obtained_score / total_score
    if ratio >= 0.9:
        return "本题作答情况很好。"
    elif 0.75 <= ratio < 0.9:
        return "本题作答情况较好。"
    elif 0.5 <= ratio < 0.75:
        return "本题作答情况一般。"
    else:
        return "本题作答情况较差。"


def get_accurate_content(line_result, accurate_sum, not_entirely_accurate_sum):
    """
    获取 内容是否全面，要点是否准确 内容
    """
    try:
        # 计算要点总个数
        total_points = len(line_result)
        # 计算准确要点和不完全准确要点占比
        accurate_and_not_entirely_accurate_ratio = (accurate_sum + not_entirely_accurate_sum) / total_points if total_points > 0 else 0
        # 计算准确要点占比
        accurate_ratio = accurate_sum / total_points if total_points > 0 else 0
        # 判断内容全面等级
        if accurate_and_not_entirely_accurate_ratio >= 0.8:
            content_completeness_level = "内容全面"
        elif 0.7 <= accurate_and_not_entirely_accurate_ratio < 0.8:
            content_completeness_level = "内容基本全面"
        elif 0.5 <= accurate_and_not_entirely_accurate_ratio < 0.7:
            content_completeness_level = "内容比较全面"
        else:
            content_completeness_level = "内容不够全面"
        # 判断内容要点等级
        if accurate_ratio >= 0.8:
            content_point_level = "要点准确"
        elif 0.7 <= accurate_ratio < 0.8:
            content_point_level = "要点基本全面"
        elif 0.5 <= accurate_ratio < 0.7:
            content_point_level = "要点比较全面"
        else:
            content_point_level = "要点不够全面"
        return content_completeness_level, content_point_level
    except Exception as e:
        logger.error(f"get_accurate_content error = {e}")
        return "", ""


async def check_score(data, line_result):
    student_score = 0

    # questionKeyPoint = {tmp['keyPointId']: data['keyPoint']['questionKeyPoints'] for tmp in data['keyPoint']['questionKeyPoints']}
    # 遗漏要点定位的列表
    loss_content, accurate_sum, not_entirely_accurate_sum = [], 0, 0
    # 处理每个回答要点的分数
    for index, item in enumerate(line_result):
        # 模型不稳定性兼容，有时候会返回数字，有时候返回字符串，统一转成字符串
        point_score = get_clear_score(item['要点得分'])
        keyPointId = item['keyPointId']
        # 将关键词遗漏的进行统计
        if item['回答要点分类'].strip() == "遗漏":
            loss_content.append(keyPointId)
            # 发现模型会有将遗漏要点也给分的情况，所以当遇到遗漏时候，直接强制变成0
            point_score = 0
        elif item['回答要点分类'].strip() == "准确":
            accurate_sum += 1
        else:
            not_entirely_accurate_sum += 1

        item["要点得分"] = point_score
        student_score += point_score
    content_completeness_level, content_point_level = get_accurate_content(line_result, accurate_sum, not_entirely_accurate_sum)

    key_point_review = f"""{content_completeness_level}
{content_point_level}"""
    keyPoint_lost = loss_content

    return student_score, key_point_review, keyPoint_lost


async def check_format_score(data, format_result):
    """
    检查格式得分
    """
    format_points = data["keyPoint"]["formatPoints"]
    format_total_score = sum(format_points.values())
    if isinstance(format_result, dict):
        format_score = sum([val for key, val in format_result.items() if "得分" in key])
    else:
        format_score = 0
    return format_total_score, format_score


def separate_text_by_punctuation(text):
    """
    根据标点符号分离文本
    """
    return re.split(r'[,.?!;:]', text)