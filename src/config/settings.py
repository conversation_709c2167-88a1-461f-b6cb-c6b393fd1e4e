import configparser
import json

# 加载配置文件
config = configparser.ConfigParser()
config.read("config/config.ini")

# 加载配置文件
local_config = configparser.ConfigParser()
local_config.read("config/config.ini")
env = local_config["Environment"]["env"]


def convert_config_value(value):
    # 处理布尔和数字类型字段
    if value.lower() in {'true', 'false', 'True', 'False', 'TRUE', 'FALSE'}:
        return value.lower() == 'true'
    try:
        return int(value)
    except ValueError:
        try:
            return json.loads(value)
        except Exception as e:
            return value


config = {}
for section in local_config.sections():
    config[section] = {}
    for key, value in local_config.items(section):
        config[section][key] = convert_config_value(value)
