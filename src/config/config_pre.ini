
[Environment]
# 环境：dev、pre、prod
env = pre

[Log]
level = DEBUG
dir = logs

[RabbitMQ]
url = amqp://MjphbXFwLWNuLXRsMzJkOWpyZDAxODpMVEFJNXRGc2NoS0tBdHV5TTQxZnp3bUM=:<EMAIL>:5672/
queue = ai_answer_position_analysis_notify_pre

[MySQL]
url = mysql+pymysql://u_essay:<EMAIL>:3306/essay?charset=utf8mb4
pool_size = 5
max_overflow = 10

[WORKERS]
# FastAPI 服务进程数
fastapi = 2
consumer = 2

[AISuite]
enable_aisuite = true
username = zhaojiadi
api_key = 57f6a0e030b62e7f2787a78dd3dea49e
aisuite_url = https://pre-xoms.huatu.com/unionllm/analysis
models = {"doubao_32k":{"model_name":"Doubao-1.5-pro-32k", "model_version":"250115"}, "doubao_256k":{"model_name":"Doubao-1.5-pro-256k", "model_version":"250115"}, "doubao_32k_search":{"model_name":"Doubao-1.5-pro-32k-search", "model_version":"250115"}, "doubao_256k_search":{"model_name":"Doubao-1.5-pro-256k-search", "model_version":"250115"}, "doubao_thinking_pro_m":{"model_name":"Doubao-1.5-thinking-pro", "model_version":"m-250428"}, "doubao_thinking_pro":{"model_name":"Doubao-1.5-thinking-pro", "model_version":"250415"}, "doubao_thinking_vision_pro":{"model_name":"Doubao-1.5-thinking-vision-pro", "model_version":"250428"}, "doubao_deepseek_r1":{"model_name":"huoshan_DeepSeek-R1", "model_version":"250120"}, "qwen_plus":{"model_name":"通义千问-plus", "model_version":""},"qwen_max":{"model_name":"通义千问-Max", "model_version":""},"qwen_long":{"model_name":"通义千问-long", "model_version":""}, "qwen_vl_max_latest":{"model_name":"通义千问VL-Max-latest", "model_version":""}, "qwen_3_235b_a22b":{"model_name":"qwen3-235b-a22b", "model_version":""}, "openai":{"model_name":"gpt-4o", "model_version":"241120"}}

[LLMParams]
temperature = 0
seed = 42
max_tokens = 8192
doubao_max_tokens = 12288
openai_max_tokens = 16384

[KUAKE]
url = https://scan-business.quark.cn/vision
client_id = test_huatu0306
client_secret = testXLi8UY

[oss]
access_key_id = LTAI5tQssy8XSLwp6ZqSnZ1p
access_key_secret = ******************************
endpoint = oss-cn-beijing.aliyuncs.com
bucket_name = ht-ai-t

[KAFKA]
bootstrap_servers = 172.22.0.140:9092,172.22.0.141:9092,172.22.0.142:9092
group_id = ai_essay_pre
insp_data_topic = ai_athena_correct_submit_pre
insp_data_result_topic = ai_athena_correct_result_notify_pre
offset_reset = earliest
