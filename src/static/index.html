<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI申论批改-要点定位</title>
    <!-- 引入 Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 2rem;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>AI申论批改-要点定位</h1>
    <form id="myForm">
        <div class="mb-3">
            <label for="inputText" class="form-label">输入JSON格式的题目文本信息</label>
            <textarea class="form-control" id="inputText" rows="5" placeholder="请输入文本"></textarea>
        </div>
        <button type="submit" class="btn btn-primary">提交</button>
    </form>
    <div id="result" class="mt-3"></div>
</div>

<!-- 引入 Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('myForm').addEventListener('submit', async function(event) {
            event.preventDefault(); // 阻止表单默认提交行为

            const inputText = document.getElementById('inputText').value;
            // 显示提示信息，告知用户请求已提交
            document.getElementById('result').innerHTML = '<p>请求已经提交，请勿重复点击。</p>';

            try {
                const parsedJson = JSON.parse(inputText);

                // 发送 POST 请求
                const response = await fetch('/location/analysis', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(parsedJson)
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }

                // 读取流式数据
                const reader = response.body.getReader();
                let result = '';
                let partialData = '';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    try {
                        console.log(value);
                        const chunk = new TextDecoder().decode(value);
                        result += chunk.replace(/\n/g, '<br>');
                        document.getElementById('result').innerHTML = `<p>${result}</p>`;
                    } catch (error) {
                        // 继续收集数据直到可以解析
                    }
                }
            } catch (error) {
                console.error('Error processing request:', error);
                document.getElementById('result').innerHTML = `<p>发生错误：${error.message}</p>`;
            }
        });
    </script>
</body>
</html>