version: 1
disable_existing_loggers: False
formatters:
  standard:
    format: '%(asctime)s - %(levelname)s - %(message)s'
handlers:
  file_handler:
    class: logging.FileHandler
    filename: logs/uvicorn.log
    formatter: standard
    level: DEBUG
  # console_handler:
  #   class: logging.StreamHandler
  #   formatter: standard
  #   level: DEBUG
loggers:
  uvicorn:
    handlers: [file_handler]
    level: INFO
    propagate: False
  uvicorn.error:
    handlers: [file_handler]
    level: ERROR
    propagate: False
  uvicorn.access:
    handlers: [file_handler]
    level: INFO
    propagate: False