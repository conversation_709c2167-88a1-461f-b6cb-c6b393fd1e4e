import io
import time
import json
import uuid
import base64
import hashlib
import os
import aiohttp
import PIL
from PIL import Image
from utils.logger import logger
from utils.request import fetch
from config.settings import config
from typing import List, Dict, Optional


class KuakeOCR:
    """夸克OCR识别类"""
    
    # OCR功能映射
    ocr_function_info = {
        "auto_select": "扫描文件",
        "painting_drawing_sketch": "素描速写",
        "enhance": "试卷增强",
        "certificate": "证件票据增强",
        "watermark_remover": "图像去水印",
        "handwriting_remover": "图像去手写",
        "rectification": "图像去阴影",
        "screen_enhance": "图像去屏纹",
        "work_scene": "合同整理",
        "painting_toner_saving": "提取线稿",
        "nature_high_definition": "画质增强",
        "monochrome": "文档去底色",
        "no_genre": "图像裁剪矫正",
    }
    
    # 图片限制
    max_file_size = 10  # MB
    max_image_size = 8000  # 像素
    min_image_size = 100  # 像素
    aspect_ratio_max = 50  # 长宽比最大值
    scale_factor_min = 0.9  # 最小缩放比例
    max_iterations = 30  # 最大缩放次数，防止无限循环
    
    @classmethod
    async def process_image(cls, image_data, is_url=False, function_option="auto_select"):
        """
        处理图像，支持URL和二进制数据
        
        Args:
            image_data: 图片数据，可以是URL或二进制数据
            is_url: 是否为URL，True表示URL，False表示二进制数据
            function_option: 功能选项，默认为矫正
            
        Returns:
            dict: 识别结果
        """
        try:
            if function_option not in cls.ocr_function_info:
                raise ValueError(f"不支持的OCR功能: {function_option}")
            
            # 检查并处理图片
            if is_url:
                # URL直接使用，不需要处理
                image_processed = image_data
            else:
                # 二进制数据需要检查和压缩
                image_processed = cls._check_and_compress_image(image_data)
            
            # 创建请求参数
            param = cls._create_request_param(image_processed, is_url, function_option)
            
            # 发送请求
            headers = {"Content-Type": "application/json"}
            response = await fetch(config["KUAKE"]["url"], method='POST', json=param, headers=headers)
            # 处理响应
            if response is not None:
                return response
            else:
                raise ValueError(f"未收到有效响应")
        
        except ValueError as e:
            msg = f"OCR识别异常: {str(e)}"
            logger.error(f"{msg}")
            raise Exception(msg)
        except Exception as e:
            msg = f"OCR识别异常"
            logger.error(f"{msg}: {e}")
            raise Exception(msg)
    
    @classmethod
    def _check_and_compress_image(cls, image_binary, max_file_size=None, min_image_size=None, max_image_size=None, aspect_ratio_max=None, scale_factor_min=None, max_iterations=None, is_base64=True):
        """检查图片并在需要时进行压缩"""
        # 处理二进制数据情况
        try:
            max_file_size = cls.max_file_size if max_file_size is None else max_file_size
            min_image_size = cls.min_image_size if min_image_size is None else min_image_size
            max_image_size = cls.max_image_size if max_image_size is None else max_image_size
            aspect_ratio_max = cls.aspect_ratio_max if aspect_ratio_max is None else aspect_ratio_max
            scale_factor_min = cls.scale_factor_min if scale_factor_min is None else scale_factor_min
            max_iterations = cls.max_iterations if max_iterations is None else max_iterations
            
            # 计算二进制数据大小（MB）
            file_size = len(image_binary) / (1024 * 1024)

            # 从二进制数据创建图片对象
            img = Image.open(io.BytesIO(image_binary))
            # 转换 RGBA → RGB，避免 JPEG 报错
            if img.mode == 'RGBA':
                img = img.convert('RGB')
            width, height = img.size
            
            # 检查长宽比
            aspect_ratio = max(width, height) / min(width, height)
            if aspect_ratio > aspect_ratio_max:
                raise ValueError(f"图片长宽比过大: {aspect_ratio}，最大允许{aspect_ratio_max}")
            
            # 初始化变量
            compressed_binary = image_binary
            iteration = 0
            
            # 循环缩放直到满足条件
            while (file_size > max_file_size or width > max_image_size or height > max_image_size) and iteration < max_iterations:
                # 第一次迭代时记录原始尺寸
                if iteration == 0:
                    logger.info(f"图片大小为 {file_size:.2f}MB，尺寸为 {width}x{height}，需要缩放")
                
                # 计算缩放比例
                scale_factor = 1.0
                
                # 如果尺寸超过限制，计算缩放比例
                if width > max_image_size or height > max_image_size:
                    scale_factor = max_image_size / max(width, height)
                
                # 如果文件大小超过限制，也计算缩放比例
                if file_size > max_file_size:
                    # 估算缩放比例，假设文件大小与像素数量成正比
                    size_scale = (max_file_size / file_size) ** 0.5  # 开平方，因为是二维缩放
                    scale_factor = min(scale_factor, size_scale)
                
                # 确保缩放比例不会太小
                scale_factor = max(scale_factor, scale_factor_min)
                
                # 计算新尺寸
                new_width = int(width * scale_factor)
                new_height = int(height * scale_factor)
                
                # 等比例缩放图片
                img = img.resize((new_width, new_height), Image.LANCZOS)
                
                # 保存缩放后的图片
                buffer = io.BytesIO()
                img.save(buffer, format=img.format or 'JPEG', optimize=True)
                buffer.seek(0)
                compressed_binary = buffer.getvalue()
                
                # 重新计算文件大小和尺寸
                file_size = len(compressed_binary) / (1024 * 1024)
                width, height = new_width, new_height
                
                logger.info(f"缩放后图片大小为 {file_size:.2f}MB，尺寸为 {width}x{height}")
                iteration += 1
            
            if iteration >= max_iterations:
                logger.warning(f"达到最大缩放次数 {max_iterations}，但图片仍不满足条件")
            
            # 检查图片尺寸
            if width < min_image_size or height < min_image_size:
                raise ValueError(f"图片尺寸过小: {width}x{height}，最小要求{min_image_size}x{min_image_size}像素")
            
            if is_base64:
                # 返回base64编码
                return base64.b64encode(compressed_binary).decode('utf-8')
            else:
                # 返回二进制数据
                return compressed_binary
                
        except IOError:
            raise ValueError("无法处理提供的二进制图片数据")
        except Exception as e:
            logger.error(f"图片处理过程中发生异常: {str(e)}")
            raise
    
    @classmethod
    def _get_signature(cls, business, sign_method, sign_nonce, timestamp):
        """
        生成签名
        
        Args:
            business: 业务类型
            sign_method: 签名方法
            sign_nonce: 随机字符串
            timestamp: 时间戳
            
        Returns:
            str: 签名字符串
            
        Raises:
            ValueError: 当签名方法不支持时抛出
        """
        client_id = config["KUAKE"]["client_id"]
        client_secret = config["KUAKE"]["client_secret"]
        
        raw_str = f"{client_id}_{business}_{sign_method}_{sign_nonce}_{timestamp}_{client_secret}"
        utf8_bytes = raw_str.encode("utf-8")
        
        # 根据sign_method选择不同的摘要算法
        if sign_method.lower() == "sha256":
            digest = hashlib.sha256(utf8_bytes).hexdigest()
        elif sign_method.lower() == "sha1":
            digest = hashlib.sha1(utf8_bytes).hexdigest()
        elif sign_method.lower() == "md5":
            digest = hashlib.md5(utf8_bytes).hexdigest()
        elif sign_method.lower() in ["sha3-256", "sha3_256"]:
            digest = hashlib.sha3_256(utf8_bytes).hexdigest()
        else:
            raise ValueError("不支持的签名方法")
            
        # 将摘要转换为小写十六进制字符串
        sign = digest.lower()
        return sign
    
    @classmethod
    def _create_request_param(cls, image_data, is_url=False, function_option="auto_select"):
        """
        创建请求参数
        
        Args:
            image_data: 图片数据，可以是base64编码或URL
            is_url: 是否为URL
            function_option: 功能选项，默认为矫正
            
        Returns:
            dict: 请求参数
        """
        business = "vision"
        sign_method = "SHA3-256"
        sign_nonce = uuid.uuid4().hex
        timestamp = int(time.time() * 1000)
        signature = cls._get_signature(business, sign_method, sign_nonce, timestamp)
        req_id = uuid.uuid4().hex

        param = {
            "dataType": "image",
            "serviceOption": "scan",
            "inputConfigs": json.dumps({"function_option": function_option}),
            "outputConfigs": "",
            "reqId": req_id,
            "clientId": config["KUAKE"]["client_id"],
            "signMethod": sign_method,
            "signNonce": sign_nonce,
            "timestamp": timestamp,
            "signature": signature
        }
        
        # 根据数据类型设置不同的参数
        if is_url:
            param["dataUrl"] = image_data
        else:
            param["dataBase64"] = image_data
            
        return param

async def fix_image_orientation(image_data: str) -> Optional[str]:
    """
    修正图片的方向
    Args:
        image_data: 图片的base64编码
    Returns:
        修正后的图片base64编码
    """
    try:
        # 解码base64图片
        image_bytes = base64.b64decode(image_data)
        img = Image.open(io.BytesIO(image_bytes))
        
        # 检查图片是否有EXIF数据
        if hasattr(img, '_getexif') and img._getexif() is not None:
            exif = dict(img._getexif().items())
            orientation = exif.get(0x0112)
            
            # 根据方向旋转图片
            if orientation:
                if orientation == 2:
                    img = img.transpose(Image.FLIP_LEFT_RIGHT)
                elif orientation == 3:
                    img = img.rotate(180)
                elif orientation == 4:
                    img = img.transpose(Image.FLIP_TOP_BOTTOM)
                elif orientation == 5:
                    img = img.rotate(-90).transpose(Image.FLIP_LEFT_RIGHT)
                elif orientation == 6:
                    img = img.rotate(-90)
                elif orientation == 7:
                    img = img.rotate(90).transpose(Image.FLIP_LEFT_RIGHT)
                elif orientation == 8:
                    img = img.rotate(90)
        
        # 将修正后的图片转回base64
        buffer = io.BytesIO()
        img.save(buffer, format="JPEG")
        corrected_image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return corrected_image_base64
    except Exception as e:
        logger.error(f"图片方向修正失败: {str(e)}")
        return image_data

async def merge_images(image_list: List[str]) -> Optional[str]:
    """
    将多张图片合并成一张
    Args:
        image_list: 多个图片的base64编码列表
    Returns:
        合并后的图片base64编码
    """
    try:
        if not image_list:
            return None
        
        if len(image_list) == 1:
            return image_list[0]
        
        # 解码所有图片
        images = []
        for img_data in image_list:
            img_bytes = base64.b64decode(img_data)
            img = Image.open(io.BytesIO(img_bytes))
            images.append(img)
        
        # 计算合并后的图片尺寸
        total_width = max(img.width for img in images)
        total_height = sum(img.height for img in images)
        
        # 创建新的空白图片
        merged_image = Image.new('RGB', (total_width, total_height), (255, 255, 255))
        
        # 将图片按垂直方向拼接
        y_offset = 0
        for img in images:
            # 水平居中放置
            x_offset = (total_width - img.width) // 2
            merged_image.paste(img, (x_offset, y_offset))
            y_offset += img.height
        
        # 将合并后的图片转回base64
        buffer = io.BytesIO()
        merged_image.save(buffer, format="JPEG", quality=85)  # 降低质量以控制大小
        merged_image_size = buffer.tell()
        
        # 如果图片大于10M，进一步压缩
        if merged_image_size > 10 * 1024 * 1024:
            quality = 70
            while merged_image_size > 10 * 1024 * 1024 and quality > 10:
                buffer = io.BytesIO()
                merged_image.save(buffer, format="JPEG", quality=quality)
                merged_image_size = buffer.tell()
                quality -= 10
        
        merged_image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return merged_image_base64
    except Exception as e:
        logger.error(f"图片合并失败: {str(e)}")
        return None

async def fix_and_merge_images(images: List[Dict]) -> Optional[str]:
    """
    修正并合并多张图片
    Args:
        images: 图片数据列表，每个元素包含图片的URL
    Returns:
        处理后的图片base64编码
    """
    try:
        # 下载并解码图片
        image_base64_list = []
        
        async with aiohttp.ClientSession() as session:
            for image_data in images:
                image_url = image_data.get("url")
                if not image_url:
                    logger.error("图片URL为空")
                    continue
                
                try:
                    # 下载图片
                    async with session.get(image_url) as response:
                        if response.status == 200:
                            img_bytes = await response.read()
                            img_base64 = base64.b64encode(img_bytes).decode('utf-8')
                            
                            # 修正图片方向
                            corrected_img = await fix_image_orientation(img_base64)
                            image_base64_list.append(corrected_img)
                        else:
                            logger.error(f"下载图片失败，状态码: {response.status}, URL: {image_url}")
                except Exception as e:
                    logger.error(f"处理图片失败: {str(e)}, URL: {image_url}")
                    continue
        
        if not image_base64_list:
            logger.error("没有有效的图片数据")
            return None
        
        # 合并图片
        merged_image = await merge_images(image_base64_list)
        return merged_image
    except Exception as e:
        logger.error(f"图片处理失败: {str(e)}")
        return None