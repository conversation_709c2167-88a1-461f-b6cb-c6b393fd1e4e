import oss2
import asyncio
from utils.logger import logger
from config.settings import config


class OSSService:
    """阿里云OSS服务操作类"""
    # 从配置读取OSS参数
    access_key_id = config['oss']['access_key_id']
    access_key_secret = config['oss']['access_key_secret']
    endpoint = config['oss']['endpoint']
    bucket_name = config['oss']['bucket_name']

    # 类属性
    _bucket = None
    initialized = False
    _init_lock = asyncio.Lock()  # 异步锁
    
    @classmethod
    async def initialize(cls, bucket_name=None):
        """
        初始化OSS连接
        
        Args:
            bucket_name: 可选，指定要连接的存储桶名称，覆盖配置中的默认值
            
        Returns:
            初始化成功返回True，失败返回False
        """
        if cls.initialized and (not bucket_name or bucket_name == cls.bucket_name):
            return True
            
        async with cls._init_lock:  # 使用异步锁确保并发安全
            if cls.initialized and (not bucket_name or bucket_name == cls.bucket_name):  # 双重检查
                return True
                
            # 如果提供了bucket_name，则使用提供的值
            if bucket_name:
                cls.bucket_name = bucket_name
                
            if not all([cls.access_key_id, cls.access_key_secret, cls.endpoint, cls.bucket_name]):
                logger.error("OSS configuration is incomplete, cannot initialize")
                return False
                
            try:
                auth = oss2.Auth(cls.access_key_id, cls.access_key_secret)
                cls._bucket = oss2.Bucket(auth, cls.endpoint, cls.bucket_name)
                cls.initialized = True
                logger.info(f"OSS service initialized successfully, bucket: {cls.bucket_name}")
                return True
            except Exception as e:
                logger.error(f"OSS service initialization failed: {e}")
                return False
    
    @classmethod
    async def ensure_initialized(cls, bucket_name=None):
        """
        确保OSS服务已初始化
        
        Args:
            bucket_name: 可选，指定要连接的存储桶名称，覆盖配置中的默认值
            
        Returns:
            初始化成功返回True，失败返回False
        """
        if cls.initialized and (not bucket_name or bucket_name == cls.bucket_name):
            return True
        return await cls.initialize(bucket_name)
    
    @classmethod
    async def upload_file(cls, local_file_path, object_key, bucket_name=None):
        """
        上传文件到OSS
        
        Args:
            local_file_path: 本地文件路径
            object_key: 对象存储路径
            bucket_name: 可选，指定存储桶名称
            
        Returns:
            上传成功返回OSS URL，失败返回None
        """
        if not await cls.ensure_initialized(bucket_name):
            return None
            
        try:
            result = cls._bucket.put_object_from_file(object_key, local_file_path)

            # 检查上传结果
            if result.status == 200:
                # 构建URL
                url = f"https://{cls.bucket_name}.{cls.endpoint}/{object_key}"
                logger.info(f"file uploaded successfully: {url}")
                return url
            else:
                logger.error(f"file upload failed, status code: {result.status}")
                return None
                
        except Exception as e:
            logger.error(f"file upload failed: {e}, file: {local_file_path}")
            return None
            
    @classmethod
    async def upload_bytes(cls, data, object_key, bucket_name=None):
        """
        上传二进制数据到OSS
        
        Args:
            data: 二进制数据
            object_key: 对象键名
            bucket_name: 可选，指定存储桶名称

        Returns:
            上传成功返回OSS URL，失败返回None
        """
        if not await cls.ensure_initialized(bucket_name):
            return None
            
        try:
            result = cls._bucket.put_object(object_key, data)
            
            # 检查上传结果
            if result.status == 200:
                # 构建URL
                url = f"https://{cls.bucket_name}.{cls.endpoint}/{object_key}"
                logger.debug(f"data uploaded successfully: {url}")
                return url
            else:
                logger.error(f"data upload failed, status code: {result.status}")
                return None
                
        except Exception as e:
            logger.error(f"data upload failed: {e}")
            return None

    @classmethod
    async def check_object_exists(cls, object_key, bucket_name=None):
        """
        检查对象是否存在
        
        Args:
            object_key: 对象键名
            bucket_name: 可选，指定存储桶名称
            
        Returns:
            存在返回True，不存在返回False
        """
        if not await cls.ensure_initialized(bucket_name):
            return False
            
        try:
            exists = cls._bucket.object_exists(object_key)
            return exists
        except Exception as e:
            logger.error(f"check object existence failed: {e}, object: {object_key}")
            return False
    
    @classmethod
    async def list_objects(cls, prefix='', delimiter='/', max_keys=1000, bucket_name=None):
        """
        列出指定路径下的文件夹和文件
        
        Args:
            prefix: 前缀路径，用于指定列出的目录
            delimiter: 分隔符，通常使用'/'表示目录分隔
            max_keys: 最大返回文件数
            bucket_name: 可选，指定存储桶名称

        Returns:
            返回元组：(文件夹列表, 文件列表)
        """
        if not await cls.ensure_initialized(bucket_name):
            return [], []
            
        try:
            # 获取所有结果
            result = cls._bucket.list_objects(prefix=prefix, delimiter=delimiter, max_keys=max_keys)
            
            # 提取文件夹
            folders = [p for p in result.prefix_list]
            
            # 提取文件
            files = [obj.key for obj in result.object_list]
            
            logger.info(f"Listed objects in {prefix}: {len(folders)} folders, {len(files)} files")
            return folders, files
            
        except Exception as e:
            logger.error(f"List objects failed: {e}, prefix: {prefix}")
            return [], []
    
    @classmethod
    async def list_all_objects(cls, prefix='', delimiter='/', max_keys=1000, bucket_name=None):
        """
        列出指定路径下的所有文件（支持分页获取大量文件）
        
        Args:
            prefix: 前缀路径，用于指定列出的目录
            delimiter: 分隔符，通常使用'/'表示目录分隔
            bucket_name: 可选，指定存储桶名称
            
        Returns:
            包含所有对象的列表
        """
        if not await cls.ensure_initialized(bucket_name):
            return []
            
        try:
            all_objects = []
            next_marker = None
            
            while True:
                # 获取一批文件
                result = cls._bucket.list_objects(prefix=prefix, delimiter=delimiter, 
                                            marker=next_marker, max_keys=max_keys)
                
                # 添加到列表
                all_objects.extend(result.object_list)
                logger.info(f"Listed {len(result.object_list)} objects, total: {len(all_objects)}")
                
                # 检查是否有更多文件
                if result.is_truncated:
                    next_marker = result.next_marker
                else:
                    break

            logger.info(f"Listed all objects in {prefix}: found {len(all_objects)} objects")
            return all_objects
            
        except Exception as e:
            logger.error(f"List all objects failed: {e}, prefix: {prefix}")
            return []
        
    @classmethod
    async def get_object_metadata(cls, object_key, bucket_name=None):
        """
        获取对象的元数据
        
        Args:
            object_key: OSS对象键名
            bucket_name: 可选，指定存储桶名称
            
        Returns:
            返回对象的元数据，失败返回None
        """
        if not await cls.ensure_initialized(bucket_name):
            return None
            
        try:
            meta = cls._bucket.get_object_meta(object_key)
            return meta
        except Exception as e:
            logger.error(f"Get object metadata failed: {e}, object: {object_key}")
            return None
        
    @classmethod
    async def download_file(cls, object_key, local_file_path, bucket_name=None):
        """
        下载OSS对象到本地文件
        
        Args:
            object_key: OSS对象键名
            local_file_path: 保存到本地的文件路径
            bucket_name: 可选，指定存储桶名称
        Returns:
            下载成功返回True，失败返回False
        """
        if not await cls.ensure_initialized(bucket_name):
            return False
            
        try:
            # 下载文件
            result = cls._bucket.get_object_to_file(object_key, local_file_path)
            
            if result.status == 200:
                logger.info(f"File downloaded successfully: {object_key} -> {local_file_path}")
                return True
            else:
                logger.error(f"File download failed, status code: {result.status}, {object_key} -> {local_file_path}")
                return False
                
        except Exception as e:
            logger.error(f"File download failed: {e}, {object_key} -> {local_file_path}")
            return False
    
    @classmethod
    async def get_object_content(cls, object_key):
        """
        获取对象内容（二进制数据）
        
        Args:
            object_key: OSS对象键名

        Returns:
            返回对象的二进制内容，失败返回None
        """
        if not await cls.ensure_initialized():
            return None
            
        try:
            result = cls._bucket.get_object(object_key)
            if result.status == 200:
                content = result.read()
                logger.info(f"Object content retrieved successfully: {object_key}")
                return content
            else:
                logger.error(f"Get object content failed, status code: {result.status}")
                return None
                
        except Exception as e:
            logger.error(f"Get object content failed: {e}, object: {object_key}")
            return None