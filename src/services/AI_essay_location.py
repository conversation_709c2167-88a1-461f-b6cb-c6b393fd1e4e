import re
import json
import asyncio
from datetime import datetime
from utils.logger import logger
from services.mq import MQService
from utils.general_tools import *
from config.settings import config
from services.ai_service import AIService
from services.prompt import contentPosition
from services.analysis_manager import AnalysisManager
from openai.types.chat.chat_completion_chunk import ChatCompletionChunk


def format_text_by_p_and_br(input_text):
    """
    按 <p>、</p>、<br/> 和换行符拆分段落，并格式化为「第N段: 内容」的格式。
    """
    formatted_text = "第1段：" + input_text.strip()
    try:
        paragraphs = re.split(r'(?:<\s*p\s*>|<\s*/\s*p\s*>|<\s*br\s*>|\n+)', input_text, flags=re.IGNORECASE | re.DOTALL)
        paragraphs = [remove_tags_bs4(item).strip() for item in paragraphs if remove_tags_bs4(item).strip()]
        # 生成带序号的段落
        if paragraphs:
            return '\n'.join([f'第{i + 1}段：{p}' for i, p in enumerate(paragraphs)])
        else:
            return input_text.strip()
    except Exception as ee:
        logger.error(f"format_text_by_p_and_br error = {ee}")
        return formatted_text


def process_material_list(material_list):
    """
    处理材料列表，将每个材料的内容格式化为段落，并返回指定的 JSON 格式。
    """
    result = []

    try:
        for material in material_list:
            material_id = material.get("materialId")
            material_content = material.get("materialContent", "")

            # 格式化内容
            formatted_content = format_text_by_p_and_br(material_content)

            # 构造返回结构
            result.append({
                "材料ID": material_id,
                "材料内容": formatted_content
            })
    except Exception as e:
        logger.error(f"process_material_list error = {e}")
    finally:
        return result


def answerKeys_2_chinese(answerKeys):
    """
    处理材料列表，将每个材料的内容格式化为段落，并返回指定的 JSON 格式。
    """
    result = []

    try:
        for answer_key in answerKeys:
            answer_id = answer_key.get("answerKeyId")
            answer_content = answer_key.get("answerKey")
            # 构造返回结构
            result.append({
                "要点ID": answer_id,
                "要点": answer_content
            })
    except Exception as e:
        logger.error(f"answerKeys_2_chinese error = {e}")
    finally:
        return result


async def get_prompt(data):
    """
    获取提示内容, 根据题目类型选择不同的提示模板。
    1概括归纳
    2综合分析
    3解决问题
    4应用写作
    5文章写作
    6要素分析
    7比较分析
    8词句理解
    9观点/现象评价
    """
    try:
        if data['questionType'] in [2, 6]:
            system_message = contentPosition.comprehensive_analysis()
        elif data['questionType'] in [3]:
            system_message = contentPosition.solve_problem()
        elif data['questionType'] == 4:
            system_message = contentPosition.practical_writing()
        elif data['questionType'] in [7, 8, 9]:
            system_message = contentPosition.comprehensive_prompt()
        else:
            # 概括归纳、兜底逻辑
            system_message = contentPosition.summarize_generalize()
        # 将材料进行按行拆分，拼成材料N第几段的格式。
        data['材料-拆分后'] = process_material_list(data['materialList'])
        data['要点-中文'] = answerKeys_2_chinese(data['answerKeys'])
        user_message = str({"材料": data['材料-拆分后'], "答案要点": data['要点-中文'], "作答要求": remove_tags_bs4(data['answerRequirement'])})  # user_template.format(question=data["stem"], duration=data["duration"], answer=data["answer"])
        return {'system': system_message, 'user': user_message}
    except Exception as ee:
        logger.error(f"get_prompt error = {ee}")
        return None


def from_model_get_result(chat_cls, model_name):
    """
    由于返回内容不确定是哪个模型，所以需要按照模型区分结构
    # 由于我们设置返回结果必须是json格式，如果不是json格式，有可能他给到结果触发敏感词，或者我们的Prompt里面含有敏感词。需要重试
    """
    result = False
    input_tokens, output_tokens = 0, 0
    try:
        json_data = chat_cls.to_dict()
        logger.info(f"from_model_get_result use model_name = {model_name}")
        logger.info(f"round_parse result = {json_data}")
        result = json.loads(extract_json_from_markdown(json_data["choices"][0]['message']['content']))
        input_tokens, output_tokens = json_data['usage']['prompt_tokens'], json_data['usage']['completion_tokens']
    except Exception as ee:
        logger.error(f"from_model_get_result error = {ee}")
    return result, input_tokens, output_tokens


async def round_parse(data, tokens, prompt):
    """
    根据 token 判断长度，选择合适的模型进行解析。
    如果所有模型均无法成功解析，返回敏感信息。

    Args:
        data : 输入的 token 长度。
        tokens (int): 输入的 token 长度。
        prompt (str): 待解析的提示内容。

    Returns:
        tuple: (解析结果, 使用的模型名称)。
    """
    # 根据 token 长度选择模型列表
    model_list = (
        ['doubao_256k', 'qwen_plus', 'openai', 'qwen_long']
        if tokens >= 20 * 1024 else
        ['doubao_32k', 'qwen_max', 'openai']
    )

    result = ""
    start_time = datetime.now()
    prompt_tokens, completion_tokens = 0, 0
    completion = None
    for model_name in model_list:
        try:
            # 随机选择模型提供者
            aisuite_config = config.get("AISuite", {})

            # 根据配置决定使用统一接口或特定模型接口
            if aisuite_config.get("enable_aisuite", False):
                completion = await AIService.aisuite(
                    "empty",
                    prompt=prompt,
                    model_name=model_name,
                    environment=config["Environment"]["env"],
                    stream=True
                )
            else:
                primary_model = model_name.split("_")[0]
                completion = await getattr(AIService, primary_model)(model_name, prompt, stream=True, custom_resp="chatCompletion", stream_options={"include_usage": True})
            # 检查解析结果
            if completion:
                async for chunk in completion:
                    prompt_tokens, completion_tokens = await get_use_tokens(chunk)

                    if isinstance(chunk, ChatCompletionChunk):
                        choices = json.loads(chunk.model_dump_json())['choices']
                        if choices == [] or choices[0]['delta']['content'] is None:
                            continue
                        content = choices[0]['delta']['content']
                        result += content
                        yield f"{content}"
                    elif isinstance(chunk, str):
                        result += chunk
                        yield f"{chunk}\n\n"
                    elif 'result' in chunk:  # qianfan Method
                        content = chunk['result']
                        result += content
                        yield f"{content}\n\n"

                AnalysisManager.save_log(data, prompt, result, model_name, starttime=start_time, endtime=datetime.now(), prompt_tokens=prompt_tokens, completion_tokens=completion_tokens)
                await mq_notify(data, result)
                break
        except Exception as error:
            logger.error(f"round_parse exception: {error}, model name: {model_name}")
    if not completion:
        yield "[]"


async def get_round_parse(data, tokens, prompt):
    completion = round_parse(data, tokens, prompt)
    return completion


async def mq_notify(data, content, status=2):
    message = {
        "questionId": data['questionId'],
        "bizStatus": status,  # 状态码 2-已完成 3-生成失败
        "version": data['version'],
        "content": content,  # 内容
        "answerKeyIds": data['answerKeyIds'],
    }
    logger.info(f"MQ result is {message}")

    await MQService.send_analysis(message)


async def run(request, data):
    try:
        logger.info(f"start eassy location, data = {json.dumps(data, ensure_ascii=False)}")
        # 获取prompt
        prompt = await get_prompt(data)
        if not prompt:
            raise Exception(f"获取模板失败，请检查。")

        # 根据输入内容，根据字符长度判断，进而选择第一步模型
        tokens = await get_tokens(prompt)
        if not tokens:
            raise Exception(f"获取token数量，失败，请检查。")

        # 进行定位
        completion = await get_round_parse(data, tokens, prompt)

        if completion == '[]':
            raise Exception(f"模型调用结果失败，请检查。")
        elif completion:
            async for msg in completion:
                yield msg
            logger.info(f"题目: {data['questionId']} 解析完成")
        else:
            raise Exception(f"模型调用结果失败，请检查。")


    except asyncio.CancelledError:
        if await request.is_disconnected():
            logger.error("Stream cancelled due to client disconnection. Start async task to perform.")
            # 客户端断开连接后，重新执行异步任务解析
            await perform_async_task(data)
    except Exception as e:
        logger.error(f"run error = {e}, request_data = {data}")
        yield "[]"
        await mq_notify(data, "[]", 3)


async def perform_async_task(data):
    """
    处理异步任务执行过程中的异常情况，当出现 asyncio.CancelledError 且客户端断开连接时，重新执行异步任务
    """

    async def async_task():
        async for _ in run(None, data):
            pass
        logger.info("Async task completed")

    asyncio.create_task(async_task())
