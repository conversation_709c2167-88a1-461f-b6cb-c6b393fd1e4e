# encoding: utf-8
import json
import asyncio
from datetime import datetime
from aiokafka import AIOKafkaProducer
from kafka.admin import KafkaAdminClient, NewTopic
from config.settings import config
from utils.logger import logger


# 修改后的JSON序列化器
def json_serializer(value):
    def custom_serializer(obj):
        """处理特定类型的序列化"""
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, bytes):
            # 将bytes类型解码为字符串（根据业务需求调整）
            return obj.decode('utf-8', errors='replace')
        raise TypeError(f"Type {type(obj)} not serializable")

    try:
        # 先尝试常规序列化，遇到无法处理的类型时使用custom_serializer
        return json.dumps(value, default=custom_serializer).encode('utf-8')
    except TypeError as e:
        logger.error(f"Serialization error: {str(e)}")


class KafkaProducer:
    _instance = None
    _lock = asyncio.Lock()

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(KafkaProducer, cls).__new__(cls)
        return cls._instance

    @classmethod
    async def initialize(cls):
        async with cls._lock:
            if not hasattr(cls, 'producer'):
                cls.bootstrap_servers = config['KAFKA']['bootstrap_servers']
                cls.topic = config['KAFKA']['insp_data_result_topic']
                await cls.ensure_topic_exists()
                cls.producer = AIOKafkaProducer(
                    bootstrap_servers=cls.bootstrap_servers,
                    value_serializer=lambda v: json.dumps(v).encode('utf-8') # 使用修改后的序列化器
                )
                await cls.producer.start()

    @classmethod
    async def ensure_topic_exists(cls):
        loop = asyncio.get_running_loop()
        await loop.run_in_executor(None, cls._create_topic_if_not_exists)

    @classmethod
    def _create_topic_if_not_exists(cls):
        admin_client = KafkaAdminClient(bootstrap_servers=cls.bootstrap_servers)
        try:
            existing_topics = admin_client.list_topics()
            if cls.topic not in existing_topics:
                num_partitions = 1
                replication_factor = 1
                new_topic = NewTopic(
                    name=cls.topic,
                    num_partitions=num_partitions,
                    replication_factor=replication_factor
                )
                admin_client.create_topics([new_topic])
                logger.info(f"Topic {cls.topic} created successfully.")
        except Exception as e:
            logger.error(f"Failed to create topic {cls.topic}: {e}")
        finally:
            admin_client.close()

    async def stop(self):
        if hasattr(self, 'producer'):
            await self.producer.stop()
            logger.info("Kafka producer stopped")

    @classmethod
    async def send_message(cls, message: dict):  # 强调需要传入字典类型
        """发送消息（注意：message应为字典类型）"""
        try:
            await cls.initialize()
            await cls.producer.send(cls.topic, message)
            logger.debug(f"Message sent to topic {cls.topic}")
        except Exception as e:
            logger.error(f"Failed to send message to Kafka: {e}")
