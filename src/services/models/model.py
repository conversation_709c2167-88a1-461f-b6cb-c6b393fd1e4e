from sqlalchemy import Column, Integer, String, Text, DateTime, BigInteger
from services.models.base_model import BaseModel


class EssayLocationLog(BaseModel):
    __tablename__ = 'essay_location_log'
    id = Column(Integer, primary_key=True, autoincrement=True)
    raw_data = Column(Text)
    question_id = Column(Integer, nullable=True, comment='题目id')
    model_name = Column(String(100), nullable=False)
    prompt = Column(Text, nullable=False)
    result = Column(Text)
    created_at = Column(DateTime, nullable=False)
    finished_at = Column(DateTime, nullable=False)
    prompt_tokens = Column(BigInteger, nullable=False)  # 使用 BigInteger 对应 unsigned int
    completion_tokens = Column(BigInteger, nullable=False)  # 使用 BigInteger 对应 unsigned int


class EssayCorrectionLog(BaseModel):
    __tablename__ = 'essay_correction_log'
    id = Column(Integer, primary_key=True, autoincrement=True)
    question_id = Column(Integer, nullable=False, comment='题目id')
    answer_id = Column(Integer, nullable=False, comment='答题卡id')
    raw_data = Column(Text, comment='工程请求的原始数据')
    model_name = Column(String(100), nullable=False, comment='模型名称')
    prompt = Column(Text, nullable=False, comment='解析提示词')
    result = Column(Text, comment='AI解析结果')
    step_num = Column(Integer, nullable=False, comment='处理步骤，2 要点划线批改 3 逻辑批改 7 总体点评')
    created_at = Column(DateTime, nullable=False, comment='解析开始时间')
    finished_at = Column(DateTime, nullable=False, server_default='CURRENT_TIMESTAMP', comment='解析结束时间')
    prompt_tokens = Column(Integer, nullable=False, comment='输入内容的token数量')
    completion_tokens = Column(Integer, nullable=False, comment='输出内容的token数量')
