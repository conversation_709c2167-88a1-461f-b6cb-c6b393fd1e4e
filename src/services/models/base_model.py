from sqlalchemy import create_engine, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from config.settings import config

# 数据库连接配置
mysql_config = config['MySQL']
pool_size = int(mysql_config.get('pool_size'))
max_overflow = int(mysql_config.get('max_overflow'))

# 创建数据库引擎
engine = create_engine(mysql_config.get('url'), pool_size=pool_size, max_overflow=max_overflow)

# 创建会话工厂
Session = sessionmaker(bind=engine)

# 创建基类
Base = declarative_base()

class BaseModel(Base):
    __abstract__ = True

    @classmethod
    def insert(cls, **data):
        """
        插入数据到数据库。
        :param data: 要插入的数据
        """
        with Session() as session:
            instance = cls(**data)
            session.add(instance)
            session.commit()

    @classmethod
    def delete(cls, condition):
        """
        删除数据。
        :param condition: 删除条件
        """
        with Session() as session:
            query = session.query(cls).filter(condition)
            query.delete()
            session.commit()

    @classmethod
    def update(cls, condition, data):
        """
        更新数据。
        :param condition: 更新条件
        :param data: 要更新的数据
        """
        with Session() as session:
            query = session.query(cls).filter(condition)
            query.update(data)
            session.commit()
    
    @classmethod
    def _query(cls, condition=None, fetchone=False):
        """
        查询数据。
        :param condition: 查询条件
        :return: 查询结果
        """
        with Session() as session:
            query = session.query(cls)
            if condition is None:
                if fetchone: return query.first()
                return query.all()
            else:
                if type(condition) is list:
                    for c in condition:
                        query = query.filter(c)
                else:
                    query = query.filter(condition)
                if fetchone: return query.first()
                return query.all()
        return False

    @classmethod
    def query(cls, condition=None):
        return cls._query(condition)
    
    @classmethod
    def first(cls, condition=None):
        return cls._query(condition, True)

    @classmethod
    def build_and_execute_query(cls, query_builder):
        """
        构建并执行查询。
        :param query_builder: 查询构建器函数
        :return: 查询结果
        """
        with Session() as session:
            compiled_query = query_builder.statement.compile(compile_kwargs={"literal_binds": True})
            result = session.execute(text(str(compiled_query)))
            rows = result.fetchall()
        
        return rows

    @classmethod
    def query_builder(cls):
        return Session().query(cls)
    
    @classmethod
    def get_sql(cls, conditions):
        """
        获取 SQL 语句。
        :param condition: 查询条件
        :param session: 当前会话
        :return: SQL 语句字符串
        """
        with Session() as session:
            query = session.query(cls)
            if conditions:
                for condition in conditions:
                    query = query.filter(condition)
        return str(query.statement.compile(compile_kwargs={"literal_binds": True}))
    
    def __repr__(self):
        """
        返回对象的字符串表示形式。
        """
        columns = self.__table__.columns.keys()
        attrs = ', '.join(f'{col}={getattr(self, col)}' for col in columns)
        return f"<{self.__class__.__name__}({attrs})>"