import json
import random
from . import ai_providers
from datetime import datetime
from utils.logger import logger
from config.settings import config
from utils.tools import async_timing_decorator
from openai.types.chat.chat_completion import ChatCompletion
from openai.types.chat.chat_completion_chunk import ChatCompletionChunk
from utils.request import call_dashscope, call_openai, call_qianfan, ali_text_moderation, call_doubao_tokens, call_aisuite


class AIService:
    @staticmethod
    async def get_content_from_stream(completion):
        if completion is None:
            return ""

        if isinstance(completion, ChatCompletion):
            return completion.choices[0].message.content

        content = ""
        async for chunk in completion:
            if isinstance(chunk, ChatCompletionChunk):  # OpenAI Method
                choices = json.loads(chunk.model_dump_json())['choices']
                if choices == [] or choices[0]['delta']['content'] is None:
                    continue
                content += choices[0]['delta']['content']
            elif 'result' in chunk:  # qianfan Method
                content += chunk['result']
            else:
                content += chunk
        return content

    @staticmethod
    async def request(provider, prompt, is_aisuite=False, is_count_tokens=False, is_dashscope=False, model_name="", **kwargs):
        """
        处理大模型提供者的请求。

        Args:
            prompt (str): 用户输入的提示文本。
            stream (bool, optional): 是否以流式方式返回结果。默认为 False。
            response_format (str, optional): 响应格式，默认为 "text"。可选：chatCompletion

        Returns:
            tuple: 包含开始时间、结束时间和内容的元组。
            chatCompletion: OpenAI chatCompletion 对象。
        """

        start_time = datetime.now()
        custom_resp = kwargs.get("custom_resp", "text")
        # 删除自定义参数
        if 'custom_resp' in kwargs:
            del kwargs['custom_resp']

        if is_count_tokens:
            return await call_doubao_tokens(provider, prompt)

        # 从配置文件获取默认参数
        default_params = {
            "temperature": config["LLMParams"]["temperature"],
            "seed": config["LLMParams"]["seed"],
            "max_tokens": config["LLMParams"]["max_tokens"]
        }

        # 读取配置文件，获取Project配置
        aisuite_config = config["AISuite"]
        # 非aisuite模式，按照已有流程进行处理。
        if not(aisuite_config.get("enable_aisuite", False) and is_aisuite):
            if "doubao" in provider.get_name():
                kwargs["max_tokens"] = config["LLMParams"]["doubao_max_tokens"]
            elif "openai" in provider.get_name():
                kwargs["max_tokens"] = config["LLMParams"]["openai_max_tokens"]
                
            # 使用传入的参数更新默认参数
            kwargs = {**default_params, **kwargs}

            if is_dashscope:
                call_func = call_dashscope
            elif "qianfan" in provider.get_name():
                call_func = call_qianfan
            else:
                call_func = call_openai
            
            # 直接返回OpenAI的chatCompletion对象
            if custom_resp == "chatCompletion":
                return await call_func(provider, prompt, **kwargs)

            # 返回tuple(开始时间, 结束时间, 字符串结果)
            completion = await call_func(provider, prompt, **kwargs)
            content = await AIService.get_content_from_stream(completion)

            return (start_time, datetime.now(), content)
        
        # 调用aisuite
        if "doubao" in model_name:
            kwargs["max_tokens"] = config["LLMParams"]["doubao_max_tokens"]
        elif "openai" in model_name:
            kwargs["max_tokens"] = config["LLMParams"]["openai_max_tokens"]
        
        # 使用传入的参数更新默认参数
        kwargs = {**default_params, **kwargs}
        return await call_aisuite(aisuite_config, model_name, prompt=prompt, **kwargs)

    @staticmethod
    @async_timing_decorator
    async def doubao(model_name, prompt, **kwargs):
        provider = random.choice(ai_providers.get_provider_by_name(model_name))
        if provider:
            # 随机取出一个豆包的节点，32k 128k通用
            return await AIService.request(provider, prompt, **kwargs)
        logger.error("Doubao provider not found")
        return None

    @staticmethod
    @async_timing_decorator
    async def aisuite(provider, prompt, model_name, **kwargs):
        if provider:
            return await AIService.request(provider, prompt, is_aisuite=True, model_name=model_name, **kwargs)
        logger.error("AISuite error ")
        return None

    @staticmethod
    @async_timing_decorator
    async def qwen(model_name, prompt, **kwargs):
        provider = random.choice(ai_providers.get_provider_by_name(model_name))

        if provider:
            return await AIService.request(provider, prompt, **kwargs)
        logger.error("Qwen provider not found")
        return None
    
    @staticmethod
    @async_timing_decorator
    async def dashscope(model_name, prompt, **kwargs):
        provider = random.choice(ai_providers.get_provider_by_name(model_name))

        if provider:
            return await AIService.request(provider, prompt, is_dashscope=True, **kwargs)
        logger.error("Dashscope provider not found")
        return None
    
    @staticmethod
    @async_timing_decorator
    async def openai(model_name, prompt, **kwargs):
        provider = random.choice(ai_providers.get_provider_by_name(model_name))
        if provider:
            return await AIService.request(provider, prompt, **kwargs)
        logger.error("OpenAI provider not found")
        return None

    @staticmethod
    @async_timing_decorator
    async def count_tokens(provider, prompt, **kwargs):
        if provider:
            return await AIService.request(provider, prompt, is_count_tokens=True, **kwargs)
        logger.error("count_tokens provider not found")
        return None

    @staticmethod
    @async_timing_decorator
    async def qianfan(provider, prompt, **kwargs):
        if provider:
            return await AIService.request(provider, prompt, **kwargs)
        logger.error("Qianfan provider not found")
        return None

    @staticmethod
    @async_timing_decorator
    async def ali_text_moderation(provider, content):
        if provider:
            return await ali_text_moderation(provider, content)
        logger.error("Ali provider not found")
        return None
