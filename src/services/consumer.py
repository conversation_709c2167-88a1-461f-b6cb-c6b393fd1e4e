import asyncio
import multiprocessing
from datetime import datetime
import json
import time
from multiprocessing import cpu_count, Process
from aiokafka import AIOKafkaConsumer, ConsumerStoppedError, TopicPartition
from aiokafka.errors import CommitFailedError
from fastapi import FastAPI
from config.settings import config
from services.AI_essay_correction import run
from services.producer import KafkaProducer
from utils.logger import logger


def json_serializer(obj):
    if isinstance(obj, datetime):
        return obj.isoformat()
    raise TypeError(f"Type {type(obj)} not serializable")


class KafkaConsumer:
    def __init__(self):
        self.KAFKA_BOOTSTRAP_SERVERS = config.get("KAFKA").get('bootstrap_servers')
        self.KAFKA_GROUP_ID = config.get("KAFKA").get("group_id")
        self.KAFKA_TOPIC = [
            config.get("KAFKA").get("insp_data_topic"),
        ]
        self.KAFKA_OFFSET_RESET = config.get("KAFKA").get("offset_reset")
        self.last_insert_time = time.time()

    async def consume_messages(self):
        consumer = AIOKafkaConsumer(
            *self.KAFKA_TOPIC,
            loop=asyncio.get_event_loop(),
            bootstrap_servers=self.KAFKA_BOOTSTRAP_SERVERS,
            group_id=self.KAFKA_GROUP_ID,
            auto_offset_reset=self.KAFKA_OFFSET_RESET,
            enable_auto_commit=False,  # 禁用自动提交
            max_poll_records=10,  # 每次poll的最大记录数
            session_timeout_ms=30000,  # 会话超时时间
            max_poll_interval_ms=600000,  # 最大poll间隔
            heartbeat_interval_ms=5000,  # 心跳间隔
        )

        try:
            await consumer.start()
            async for msg in consumer:
                logger.info(f"Received RAW TOPIC: {msg.topic}, PARTITION: {msg.partition}")

                # 严格校验主题名称
                if msg.topic not in self.KAFKA_TOPIC:
                    logger.error(f"收到未知主题消息: {msg.topic}")
                    continue

                # 增加主题专用字段验证
                try:
                    log_data = json.loads(msg.value.decode('utf-8'))
                    # 整合消息信息到一条日志
                    logger.info(f"消息ID: {log_data.get('answerId')}, 分区: {msg.partition}, 消费前偏移量: {msg.offset}")
                    await self.process_insp_data(log_data)
                    # 确保在成功处理后提交偏移量
                    await consumer.commit()
                    # 获取提交后的实际偏移量
                    topic_partition = TopicPartition(msg.topic, msg.partition)
                    committed_offset = await consumer.committed(topic_partition)
                    logger.info(f"消息ID: {log_data.get('answerId')}, 分区: {msg.partition}, 消费后偏移量: {committed_offset}")
                except Exception as e:
                    logger.error(f"处理消息时出错: {e}", exc_info=True)
                    # 这里可以选择不提交偏移量以便重试
        except ConsumerStoppedError:
            logger.error("消费者意外停止")
        except Exception as e:
            logger.error(f"Kafka消费错误: {e}", exc_info=True)
        finally:
            logger.info(f"偏移量提交成功，即为消费之后的偏移量: {msg.offset+1}")  # 确保在处理完消息后立即提交偏移量
            await consumer.stop()

    async def commit_offset(self, consumer, msg):
        """提交消息偏移量，并处理提交失败的情况"""
        try:
            # 创建 TopicPartition 对象
            topic_partition = TopicPartition(msg.topic, msg.partition)
            offsets = {topic_partition: msg.offset + 1}
            await consumer.commit(offsets=offsets)
            logger.info(f"成功提交偏移量 {msg.offset + 1} 对于主题 {msg.topic} 分区 {msg.partition}")
        except CommitFailedError as cfe:
            logger.error(f"提交偏移量失败，消息偏移量 {msg.offset}: {cfe}", exc_info=True)
            # 可以添加重试逻辑，这里简单记录错误
        except Exception as e:
            logger.error(f"提交偏移量时出现未知错误，消息偏移量 {msg.offset}: {e}", exc_info=True)

    async def process_insp_data(self, log_data):
        """处理AI检测数据"""
        try:
            result, cost_time = await run(log_data)
            logger.info(f"AI数据处理成功: {log_data.get('answerId')}, 消耗时间: {cost_time}")
            await KafkaProducer().send_message(result)
        except Exception as e:
            logger.error(f"AI数据处理失败: {e}")
            raise  # 向上抛出异常以触发消息重试

    def run_consumer(self):
        """启动消费者实例"""
        logger.info("消费者进程启动")
        try:
            asyncio.run(self.consume_messages())
        except KeyboardInterrupt:
            logger.info("消费者安全退出")

    def start_processes(self, max_process=2):
        """启动多进程消费"""

        self.MAX_PROCESSES = max_process  # 明确指定人工主题的分区数

        processes = []
        logger.info(f"将进行{self.MAX_PROCESSES}个进程进行消费。")

        # 根据CPU核心数动态创建进程
        for _ in range(self.MAX_PROCESSES):
            p = Process(target=self.run_consumer)  # 确保每个进程有独立的消费者实例
            p.daemon = True
            p.start()
            processes.append(p)

        try:
            for p in processes:
                p.join()
        except KeyboardInterrupt:
            logger.info("终止信号接收，关闭消费者进程")


if __name__ == "__main__":
    kafka_consumer = KafkaConsumer()
    kafka_consumer.start_processes()
