import asyncio
import aio_pika
import json
from utils.logger import logger
from config.settings import config

rabbitmq_config = config['RabbitMQ']

class MQService:
    connection = None
    _lock = asyncio.Lock()

    @classmethod
    async def initialize_connection(cls):
        async with cls._lock:
            try:
                if cls.connection is None:
                    cls.connection = await aio_pika.connect_robust(rabbitmq_config.get('url'))
            except Exception as e:
                logger.error(f"mq connection error {e}")

    @classmethod
    async def send_analysis(cls, result):
        await cls.initialize_connection()

        try:
            channel = await cls.connection.channel()
            queue = await channel.declare_queue(rabbitmq_config.get('queue'), durable=False)

            # 将 JSON 数据编码为字节串
            message_body = json.dumps(result, ensure_ascii=False).encode('utf-8')
            
            success = await channel.default_exchange.publish(
                aio_pika.Message(
                    body=message_body,
                    content_type="application/json",
                    delivery_mode=2
                ),
                routing_key=queue.name
            )
            if success:
                logger.info(f"#{result['questionId']} message published successfully.")
        except Exception as e:
            logger.error(f"send_analysis_to_mq: {e}, result = {result}")
