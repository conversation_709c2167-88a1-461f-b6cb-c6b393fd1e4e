from services import ai_providers
from services.models.model import EssayCorrectionLog, EssayLocationLog
from utils.logger import logger


class AnalysisManager:
    @staticmethod
    def save_log(data, prompt, result, model_name, starttime=None, endtime=None, prompt_tokens=0, completion_tokens=0):
        try:
            EssayLocationLog.insert(
                raw_data=str(data) if not isinstance(data, str) else data,
                question_id=data['questionId'],
                model_name=model_name,
                prompt=str(prompt) if not isinstance(prompt, str) else prompt,
                result=str(result) if not isinstance(result, str) else result,
                created_at=starttime,
                finished_at=endtime,
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens

            )
        except Exception as e:
            logger.error(f"InterviewAnalysisLog {e}")

    @staticmethod
    def save_correction_log(data, prompt, result, model_name, starttime=None, endtime=None, step_num=2, prompt_tokens=0, completion_tokens=0):
        try:
            EssayCorrectionLog.insert(
                question_id=data['questionId'],
                answer_id=data['answerId'],
                raw_data=str(data) if not isinstance(data, str) else data,
                model_name=model_name,
                prompt=str(prompt) if not isinstance(prompt, str) else prompt,
                result=str(result) if not isinstance(result, str) else result,
                step_num=step_num,
                created_at=starttime,
                finished_at=endtime,
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens
            )
        except Exception as e:
            logger.error(f"InterviewAnalysisLog {e}")
