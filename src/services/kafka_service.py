import json
import time
import signal
import asyncio
from utils.logger import logger
from config.settings import config
from multiprocessing import Process
from services.AI_essay_correction import run
from aiokafka.errors import CommitFailedError, KafkaError
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer, TopicPartition
from aiokafka.consumer.subscription_state import ConsumerRebalanceListener


class AIOConsumerRebalanceListener(ConsumerRebalanceListener):
    """处理rebalance事件的监听器"""
    def __init__(self, on_revoke_cb, on_assign_cb):
        self.on_revoke_cb = on_revoke_cb
        self.on_assign_cb = on_assign_cb
    
    async def on_partitions_revoked(self, revoked):
        if self.on_revoke_cb:
            await self.on_revoke_cb(revoked)
    
    async def on_partitions_assigned(self, assigned):
        if self.on_assign_cb:
            await self.on_assign_cb(assigned)


class KafkaConsumerService:
    _consumer = None
    _partitions_assigned_delay = 5  # 多台服务器消费者加入时间不一致时，可能短时间内多次rebalance并 partitions_assigned，所以需要延迟消费时间(秒)
    running = False
    topics = None
    
    @classmethod
    async def initialize(cls, group_id, topics, batch_size=1, batch_timeout=1.0):
        """初始化消费者"""
        if cls._consumer is not None:
            return
            
        cls.group_id = group_id
        cls.topics = topics
        cls.batch_size = batch_size
        cls.batch_timeout = batch_timeout
        cls.running = True
        
        cls._consumer = AIOKafkaConsumer(
            *topics,
            bootstrap_servers=config.get("KAFKA").get('bootstrap_servers'),
            group_id=group_id,
            auto_offset_reset=config.get("KAFKA").get("offset_reset"),
            enable_auto_commit=False,
            max_poll_records=batch_size,
            session_timeout_ms=30000,
            max_poll_interval_ms=600000,
            heartbeat_interval_ms=5000,
        )
        listener = AIOConsumerRebalanceListener(
            on_revoke_cb=cls._on_partitions_revoked,
            on_assign_cb=cls._on_partitions_assigned
        )
        # 注册rebalance回调
        cls._consumer.subscribe(
            topics,
            listener=listener
        )
        
        await cls._consumer.start()
        logger.info(f"Kafka consumer initialized for topics: {topics} with group_id: {group_id}")

    @classmethod
    async def _on_partitions_revoked(cls, revoked):
        """分区被撤销时的回调"""
        logger.warning(f"Topics: {cls.topics}, Partitions revoked: {revoked}")

    @classmethod
    async def _on_partitions_assigned(cls, assigned):
        """分区被分配时的回调"""
        logger.warning(f"Topics: {cls.topics}, Partitions assigned: {assigned}")
        
        # 多台服务器消费者加入时间不一致时，可能短时间内多次rebalance并 partitions_assigned，所以需要延迟消费
        logger.info(f"Topics: {cls.topics}, Waiting {cls._partitions_assigned_delay:.2f}s after partitions_assigned...")
        await asyncio.sleep(cls._partitions_assigned_delay)

    @classmethod
    async def consume_messages(cls):
        """消费消息并批量处理"""
        if not cls._consumer:
            raise RuntimeError(f"Topics: {cls.topics}, Consumer not initialized. Call initialize() first.")
        
        try:
            # 批量拉取消息
            msgs = await cls._consumer.getmany(timeout_ms=int(cls.batch_timeout * 1000), max_records=cls.batch_size)
            return msgs
        except KafkaError as ke:
            logger.error(f"Topics: {cls.topics}, Kafka error: {ke}")
            await asyncio.sleep(1)
            return None
        except Exception as e:
            logger.error(f"Topics: {cls.topics}, Unexpected error in consumer: {e}", exc_info=True)
            await asyncio.sleep(1)
            return None
    
    @classmethod
    def _get_max_offsets(cls, messages):
        """获取每个分区的最大offset"""
        partitions = {}
        for msg in messages:
            tp = TopicPartition(msg.topic, msg.partition)
            if tp not in partitions or msg.offset > partitions[tp]:
                partitions[tp] = msg.offset
        return partitions
    
    @classmethod
    async def commit_offsets(cls, messages):
        """提交偏移量"""
        if not messages:
            return
            
        # 取出每个分区的最大偏移量
        partitions = cls._get_max_offsets(messages)
        
        # 为每个分区提交最大偏移量
        for tp, offset in partitions.items():
            try:
                await cls._consumer.commit({tp: offset + 1})  # 提交下一个偏移量
                # 获取提交后的实际偏移量
                committed_offset = await cls._consumer.committed(tp)
                logger.info(f"Committed offset {committed_offset} for Topic: {tp.topic}, Partition: {tp.partition}")
            except CommitFailedError as cfe:
                logger.error(f"Commit failed for {tp} at offset {offset}: {cfe}")
            except Exception as e:
                logger.error(f"Error committing offset for {tp}: {e}")
    
    @classmethod
    async def stop(cls):
        """停止消费者"""
        try:
            if cls._consumer:
                await cls._consumer.stop()
                cls._consumer = None
        except Exception as e:
            logger.error(f"Topics: {cls.topics}, Error stopping consumer: {e}")
        cls.running = False


class KafkaProducerService:
    _producer = None
    _lock = asyncio.Lock()
    
    @classmethod
    async def initialize(cls):
        """创建Kafka生产者"""
        async with cls._lock:
            if cls._producer is None:
                cls._producer = AIOKafkaProducer(
                    bootstrap_servers=config.get("KAFKA").get('bootstrap_servers'),
                    acks='all',
                    max_batch_size=16384,
                    linger_ms=50,
                )
                await cls._producer.start()
    
    @classmethod
    async def send_message(cls, topic, message, key=None):
        """发送消息到指定topic"""
        try:
            if cls._producer is None:
                await cls.initialize()
            
            if isinstance(message, (dict, list)):
                message = json.dumps(message, ensure_ascii=False).encode('utf-8')
            else:
                message = str(message).encode('utf-8')
                
            key = key.encode('utf-8') if key else None
            
            await cls._producer.send_and_wait(topic, value=message, key=key)
            return True
        except Exception as e:
            logger.error(f"Failed to send message to topic {topic}: {e}", exc_info=True)
            await cls.close()
            return False
    
    @classmethod
    async def close(cls):
        """关闭producer连接"""
        try:
            if cls._producer:
                await cls._producer.stop()
                logger.info(f"Producer closed")
        except Exception as e:
            logger.error(f"Failed to close producer: {e}")
        cls._producer = None


class KafkaService:
    @classmethod
    def _signal_handler(cls, signum, frame):
        """处理系统信号"""
        logger.info(f"Received signal {signum}, shutting down...")
        cls.shutdown()

    @classmethod
    def shutdown(cls):
        """优雅关闭所有资源"""
        logger.info("Shutting down Kafka services...")
        KafkaConsumerService.running = False
    
    @classmethod
    async def _process_message(cls, msg, message_processor, produce_topic=None):
        """处理单条消息"""
        try:
            data = json.loads(msg.value.decode('utf-8'))
            # 记录消息信息
            logger.info(
                f"Processing message - Topic: {msg.topic}, "
                f"Partition: {msg.partition}, "
                f"Offset: {msg.offset}, "
                f"answerId: {data.get('answerId')}"
            )
            # 处理消息
            result, cost_time = await message_processor(data)

            logger.info(f"Process message success - Topic: {msg.topic}, "
                        f"Partition: {msg.partition}, "
                        f"Offset: {msg.offset}, "
                        f"answerId: {data.get('answerId')}, "
                        f"cost time: {cost_time}")
            return result
        except Exception as e:
            logger.error(
                f"Error processing message - Topic: {msg.topic}, "
                f"Partition: {msg.partition}, "
                f"Offset: {msg.offset}, "
                f"message: {msg.value.decode('utf-8')}, "
                f"Error: {str(e)}",
                exc_info=True
            )
            return None

    @classmethod
    async def process_message_batch(cls, messages, message_processor, produce_topic=None):
        """批量处理消息"""
        if not messages:
            return []
            
        # 使用asyncio.gather并发处理消息
        tasks = [
            cls._process_message(msg, message_processor, produce_topic)
            for msg in messages
        ]
        return await asyncio.gather(*tasks, return_exceptions=True)
    
    @classmethod
    async def produce_results(cls, results, all_messages, produce_topic):
        """批量发送处理结果到目标topic"""
        try:
            for idx, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Task failed with error: {result}, Message: {all_messages[idx]}")
                    continue
                elif result is None:
                    logger.warning(f"No result from processor, Message: {all_messages[idx]}")
                    continue
                
                send_result = await KafkaProducerService.send_message(
                    topic=produce_topic,
                    message=result,
                )
                if send_result:
                    logger.info(f"Produced message to {produce_topic}, result: {result}")
                else:
                    logger.warning(f"Failed to produce message to {produce_topic}, result: {result}")
        except Exception as e:
            logger.error(f"Failed to produce results: {e}", exc_info=True)
    
    @classmethod
    async def start_consumer_loop(cls, group_id, topics, message_processor, produce_topic=None, batch_size=1):
        """
        启动消费者循环
        
        Args:
            group_id: 消费者组ID
            topics: 消费的主题
            message_processor: 消息处理函数
            produce_topic: 处理后发送的目标主题(可选)
            batch_size: 批处理大小
        """
        try:
            signal.signal(signal.SIGINT, cls._signal_handler)
            signal.signal(signal.SIGTERM, cls._signal_handler)
            
            # 初始化消费者
            await KafkaConsumerService.initialize(
                group_id=group_id,
                topics=topics,
                batch_size=batch_size
            )
            
            # 开始消费消息
            while KafkaConsumerService.running:
                # 批量拉取消息
                msgs = await KafkaConsumerService.consume_messages()
                if not msgs:
                    continue
                
                all_messages = []
                log_message = f"Consuming messages -"
                # 处理每个分区的消息
                for tp, messages in msgs.items():
                    if not messages:
                        continue
                    
                    """记录分区的消息消费信息"""
                    first_msg = messages[0]
                    last_msg = messages[-1]
                    log_message += f"\tTopic: {tp.topic}, Partition: {tp.partition}, Offsets: {first_msg.offset} to {last_msg.offset}, Count: {len(messages)};"                
                    all_messages.extend(messages)
                logger.info(log_message)
                    
                # 异步批量处理消息
                results = await cls.process_message_batch(
                    messages=all_messages,
                    message_processor=message_processor,
                    produce_topic=produce_topic
                )

                # 如果需要发送到其他topic
                if produce_topic:
                    await cls.produce_results(results, all_messages, produce_topic)
                    
                # 提交偏移量
                await KafkaConsumerService.commit_offsets(all_messages)
                    
        except Exception as e:
            logger.error(f"Failed in consumer loop: {e}", exc_info=True)
        finally:
            await KafkaConsumerService.stop()

    @classmethod
    def start_consuming(cls):
        """启动消费进程"""
        group_id = config["KAFKA"]["group_id"]
        topic = [config["KAFKA"]["insp_data_topic"]]
        produce_topic = config["KAFKA"]["insp_data_result_topic"]
        batch_size = config["KAFKA"]["batch_size"]
        try:
            asyncio.run(cls.start_consumer_loop(group_id, topic, run, produce_topic=produce_topic, batch_size=batch_size))
        except Exception as e:
            logger.error(f"Failed in start consuming: {e}", exc_info=True)
    
    @classmethod
    def start_processes(cls, max_threshold=1):
        """启动多个消费者进程"""
        processes = []
        
        for _ in range(max_threshold):
            p = Process(target=cls.start_consuming)
            p.start()
            processes.append(p)
        
        for p in processes:
            p.join()
