import cv2
import numpy as np
from datetime import datetime
from utils.logger import logger
from services.oss_service import OSSService


class StorageService:
    """存储服务类，提供本地和OSS存储功能"""
    
    @staticmethod
    def get_date_path():
        """
        获取当前年月路径
        
        Returns:
            年月路径字符串，格式为 card/YYYY/MM
        """
        now = datetime.now()
        return f"eassy/{now.year}/{now.month:02d}"
    
    @classmethod
    async def save_image(cls, image, file_dir, file_name, storage_mode="oss", date_path=None):
        """
        保存图像，支持多种存储模式
        
        Args:
            image: OpenCV图像对象或二进制数据
            file_dir: 文件目录
            file_name: 文件名
            storage_mode: 存储模式
            date_path: 年月路径，用于OSS存储，默认使用当前年月
            
        Returns:
            文件保存路径或OSS URL
        """
        if not isinstance(image, np.ndarray) and not isinstance(image, bytes):
            logger.error("image must be a numpy array or bytes")
            return None
        
        # OSS存储模式
        if storage_mode == "oss":
            # 如果date_path为None，则使用当前日期
            date_path = date_path if date_path is not None else cls.get_date_path()

            # 获取相对路径
            relative_dir = file_dir
            
            # 构建OSS对象键
            object_key = f"{date_path}/{relative_dir}/{file_name}"
            
            # 检查是否是OpenCV图像对象或已经是二进制数据
            if isinstance(image, np.ndarray):
                # 将图像编码为JPEG二进制数据
                ret, buffer = cv2.imencode('.jpg', image)
                if not ret:
                    logger.error(f"image encode failed: {file_name}")
                    return None
                
                # 直接上传二进制数据
                image_bytes = buffer.tobytes()
                return await OSSService.upload_bytes(image_bytes, object_key)
            else:
                return await OSSService.upload_bytes(image, object_key)
            
        # 本地存储模式
        else:
            file_path = f"{file_dir}/{file_name}"
            if isinstance(image, np.ndarray):
                cv2.imwrite(file_path, image)
            else:
                with open(file_path, 'wb') as f:
                    f.write(image)
                    
            return file_path