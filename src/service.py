import asyncio
import subprocess
import os
import signal
import time
from config.settings import config
from utils.logger import logger


class Base:
    @classmethod
    def is_running(cls, pid_file):
        """检查服务是否正在运行"""
        return cls.get_pid(pid_file) is not None

    @staticmethod
    def is_running_in_container():
        """
        检查是否在容器中运行
        """
        if os.path.exists('/proc/1/cgroup'):
            with open('/proc/1/cgroup', 'r') as f:
                cgroup_info = f.read()
                if 'docker' in cgroup_info or 'kubepods' in cgroup_info:
                    return True

        if os.path.exists('/app/is_running_in_container'):
            return True

        return False

    @classmethod
    def get_pid(cls, pid_file):
        """获取服务的 PID"""
        if os.path.exists(pid_file):
            with open(pid_file, 'r') as f:
                pid = int(f.read().strip())
            if cls.check_pid(pid):
                return pid
        return None

    @staticmethod
    def check_pid(pid):
        """检查指定 PID 是否存在"""
        try:
            os.kill(pid, 0)
        except OSError:
            return False
        else:
            return True


class FastapiManager(Base):
    """
    Fastapi 服务管理
    """

    APP = "app:EssayCorrection"  # 指定 ASGI 应用的位置
    HOST = "0.0.0.0"
    PORT = 8006
    # config.getint("WORKERS", "fastapi")
    CPU_COUNT = config['WORKERS']["fastapi"]
    LOG_CONFIG_FILE = "logging_config.yaml"
    UVICORN_CMD = f"uvicorn {APP} --host {HOST} --port {PORT} --workers {CPU_COUNT} --log-config {LOG_CONFIG_FILE}"
    CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
    # 存储进程 PID 的文件
    PID_FILE = f"{CURRENT_PATH}/logs/uvicorn.pid"

    @classmethod
    def start(cls):
        """启动服务"""
        if cls.is_running(cls.PID_FILE):
            print("Fastapi services is already running")
            return

        try:
            with open(cls.PID_FILE, 'w') as f:
                process = subprocess.Popen(cls.UVICORN_CMD.split(), stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                           preexec_fn=os.setsid)
                f.write(str(process.pid))
            print(f"Fastapi services started, PID: {process.pid}")
            if cls.is_running_in_container():
                process.wait()
        except Exception as e:
            print(f"Failed to start fastapi services: {e}")
            # if process and cls.is_running_in_container():
            #     process.terminate()
            #     process.wait()

    @classmethod
    def stop(cls):
        """停止服务"""
        pid = cls.get_pid(cls.PID_FILE)
        if pid:
            try:
                os.killpg(os.getpgid(pid), signal.SIGTERM)
                if cls.check_pid(pid):
                    os.killpg(os.getpgid(pid), signal.SIGKILL)
                os.remove(cls.PID_FILE)
                print(f"Fastapi services stopped, PID: {pid}")
            except Exception as e:
                print(f"Failed to stop fastapi services: {e}")
        else:
            print("Fastapi services is not running")

    @classmethod
    def restart(cls):
        """重启服务"""
        cls.stop()
        time.sleep(5)  # 等待服务完全停止
        cls.start()


class ConsumerManager(Base):
    """
    Kafka 消费者服务管理
    """
    WORKERS = int(config["WORKERS"]["consumer"]
                  )  # .getint("WORKERS", "consumer")
    CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
    PID_FILE = f"{CURRENT_PATH}/logs/consumer.pid"
    CONSUMER_CMD = ['python', '-c',
                    f'import services.consumer; services.consumer.KafkaConsumer().start_processes({WORKERS})']

    # python -c "import services.consumer; services.consumer.KafkaConsumer().start_processes(1)"
    @classmethod
    def start(cls):
        """启动 Kafka 消费者服务"""
        if cls.is_running(cls.PID_FILE):
            logger.info("Kafka Consumer services is already running")
            return

        try:
            with open(cls.PID_FILE, 'w') as f:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                process = subprocess.Popen(cls.CONSUMER_CMD, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                           preexec_fn=os.setsid)
                f.write(str(process.pid))
            logger.info(f"Kafka Consumer services started, PID: {process.pid}")

            print(f"Kafka Consumer services started, PID: {process.pid}")
        except Exception as e:
            logger.error(f"Failed to start Kafka Consumer services: {e}")
            print(f"Failed to start Kafka Consumer services: {e}")

    @classmethod
    def stop(cls):
        """停止 Kafka 消费者服务"""
        pid = cls.get_pid(cls.PID_FILE)
        if pid:
            try:
                os.killpg(os.getpgid(pid), signal.SIGTERM)
                if cls.check_pid(pid):
                    os.killpg(os.getpgid(pid), signal.SIGKILL)
                os.remove(cls.PID_FILE)
                print(f"Kafka Consumer services stopped, PID: {pid}")
            except Exception as e:
                print(f"Failed to stop Kafka Consumer services: {e}")
        else:
            print("Kafka Consumer services is not running")

    @classmethod
    def restart(cls):
        cls.stop()
        time.sleep(5)  # 等待服务完全停止
        cls.start()


class KafkaManager(Base):
    """
    kafka 消费者服务管理
    """
    WORKERS = int(config["WORKERS"]["kafka"])
    CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
    PID_FILE = f"{CURRENT_PATH}/logs/kafka.pid"
    KAFKA_CMD = ['python', '-c', f'import services.kafka_service; services.kafka_service.KafkaService.start_processes({WORKERS})']

    @classmethod
    def start(cls):
        """启动服务"""
        if cls.is_running(cls.PID_FILE):
            print("Kafka service is already running")
            return

        try:
            with open(cls.PID_FILE, 'w') as f:
                process = subprocess.Popen(cls.KAFKA_CMD, stdout=subprocess.PIPE, stderr=subprocess.PIPE, preexec_fn=os.setsid)
                f.write(str(process.pid))
            print(f"Kafka service started, PID: {process.pid}")
            # if cls.is_running_in_container():
            #     process.wait()
        except Exception as e:
            print(f"Failed to start kafka service: {e}")
            # if process and cls.is_running_in_container():
            #     process.terminate()
            #     process.wait()

    @classmethod
    def stop(cls):
        """停止服务"""
        pid = cls.get_pid(cls.PID_FILE)
        if pid:
            try:
                os.killpg(os.getpgid(pid), signal.SIGTERM)
                # 增加等待时间以允许 Kafka 消费者优雅关闭
                graceful_shutdown_timeout = 30
                wait_interval = 5
                elapsed_time = 0
                while cls.check_pid(pid) and elapsed_time < graceful_shutdown_timeout:
                    time.sleep(wait_interval)
                    elapsed_time += wait_interval
                    print(f"Waiting for Kafka service (PID: {pid}) to terminate... {elapsed_time}/{graceful_shutdown_timeout}s")

                if cls.check_pid(pid):
                    print(f"Kafka service (PID: {pid}) did not terminate gracefully after {graceful_shutdown_timeout}s. Forcing shutdown with SIGKILL.")
                    # 还活着，强制杀死
                    os.killpg(os.getpgid(pid), signal.SIGKILL)
                os.remove(cls.PID_FILE)
                print(f"Kafka service stopped, PID: {pid}")
            except Exception as e:
                print(f"Failed to stop kafka service: {e}")
        else:
            print("Kafka service is not running")

    @classmethod
    def restart(cls):
        cls.stop()
        cls.start()


if __name__ == "__main__":
    import sys

    if len(sys.argv) != 2:
        print("Usage: python service.py {start|stop|restart}")
    else:
        command = sys.argv[1].lower()
        if command == "start":
            logger.info("Starting KafkaManager...")
            KafkaManager.start()
            logger.info("Starting FastapiManager...")
            FastapiManager.start()
        elif command == "stop":
            KafkaManager.stop()
            FastapiManager.stop()
        elif command == "restart":
            KafkaManager.restart()
            FastapiManager.restart()
        else:
            print("Invalid command")
