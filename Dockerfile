FROM registry.htexam.com/sys/python:3.12.4

ARG ENV

# 设置工作目录
WORKDIR /app

# 复制应用代码
COPY src /app/
COPY src/config/config_$ENV.ini /app/config/config.ini

# 设置时区
RUN rm -f /etc/localtime \
  && ln -sv /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
  && echo "Asia/Shanghai" > /etc/timezone

# 安装依赖
COPY requirements.txt /app/
RUN pip install -i http://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com -r requirements.txt

# 替换默认的 APT 源为阿里云镜像源
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's|security.debian.org/debian-security|mirrors.aliyun.com/debian-security|g' /etc/apt/sources.list.d/debian.sources

# 更新 APT 包列表
RUN apt-get update

# 安装 supervisor
RUN apt-get install -y supervisor

# 安装常用命令
RUN apt-get install -y supervisor net-tools less nano vim libgl1-mesa-glx

# 创建 supervisor 配置文件
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# 启动 supervisor
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]